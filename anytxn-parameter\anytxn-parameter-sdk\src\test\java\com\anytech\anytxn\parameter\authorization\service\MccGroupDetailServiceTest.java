﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.authorization.service.MccGroupDetailServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmMccGroupDetail;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDetailMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDetailSelfMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MccGroupDetailServiceImpl的单元测试类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
class MccGroupDetailServiceTest {

    @Mock
    private MccGroupDetailMapper mccGroupDetailMapper;

    @Mock
    private MccGroupDetailSelfMapper mccGroupDetailSelfMapper;

    @InjectMocks
    private MccGroupDetailServiceImpl mccGroupDetailService;

    // 测试常量
    private static final Long TEST_ID = 12345L;
    private static final String TEST_MCC_GROUP = "RETAIL";
    private static final String TEST_MCC_CODE = "5411";
    private static final String TEST_DESCRIPTION = "超市";
    private static final String TEST_STATUS = "A";
    private static final String TEST_ORGANIZATION_NUMBER = "0001";
    private static final String TEST_UPDATE_BY = "admin";
    private static final Long TEST_VERSION_NUMBER = 1L;

    // 测试数据对象
    private ParmMccGroupDetail testParmMccGroupDetail;
    private MccGroupDetailDTO testMccGroupDetailDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 解决OrgNumberUtils静态实例初始化问题
        setupOrgNumberUtils();
        
        // 在Mock设置完成后初始化测试数据
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMocked = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMocked.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORGANIZATION_NUMBER);
            
            testParmMccGroupDetail = createTestParmMccGroupDetail();
            testMccGroupDetailDTO = createTestMccGroupDetailDTO();
        }
    }

    /**
     * 通过反射设置OrgNumberUtils静态实例
     */
    private void setupOrgNumberUtils() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);
    }

    /**
     * 创建测试用的ParmMccGroupDetail对象
     */
    private ParmMccGroupDetail createTestParmMccGroupDetail() {
        ParmMccGroupDetail detail = new ParmMccGroupDetail();
        detail.setId(TEST_ID);
        detail.setMccGroup(TEST_MCC_GROUP);
        detail.setMccCde(TEST_MCC_CODE);
        detail.setDescription(TEST_DESCRIPTION);
        detail.setStatus(TEST_STATUS);
        detail.setOrganizationNumber(TEST_ORGANIZATION_NUMBER);
        detail.setCreateTime(LocalDateTime.now());
        detail.setUpdateTime(LocalDateTime.now());
        detail.setUpdateBy(TEST_UPDATE_BY);
        detail.setVersionNumber(TEST_VERSION_NUMBER);
        return detail;
    }

    /**
     * 创建测试用的MccGroupDetailDTO对象
     */
    private MccGroupDetailDTO createTestMccGroupDetailDTO() {
        MccGroupDetailDTO dto = new MccGroupDetailDTO();
        dto.setId(TEST_ID);
        dto.setMccGroup(TEST_MCC_GROUP);
        dto.setMccCde(TEST_MCC_CODE);
        dto.setDescription(TEST_DESCRIPTION);
        dto.setStatus(TEST_STATUS);
        dto.setOrganizationNumber(TEST_ORGANIZATION_NUMBER);
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy(TEST_UPDATE_BY);
        dto.setVersionNumber(TEST_VERSION_NUMBER);
        return dto;
    }

    /**
     * 测试根据主键查询商户类别群详细信息 - 成功场景
     */
    @Test
    void testFindMccGroupDetail_Success() {
        // Arrange
        when(mccGroupDetailMapper.selectByPrimaryKey(TEST_ID))
                .thenReturn(testParmMccGroupDetail);

        // Act
        MccGroupDetailDTO result = mccGroupDetailService.findMccGroupDetail(TEST_ID);

        // Assert
        assertNotNull(result);
        assertEquals(TEST_ID, result.getId());
        assertEquals(TEST_MCC_GROUP, result.getMccGroup());
        assertEquals(TEST_MCC_CODE, result.getMccCde());
        assertEquals(TEST_DESCRIPTION, result.getDescription());
        
        verify(mccGroupDetailMapper).selectByPrimaryKey(TEST_ID);
    }

    /**
     * 测试根据主键查询商户类别群详细信息 - 记录不存在
     */
    @Test
    void testFindMccGroupDetail_NotFound() {
        // Arrange
        when(mccGroupDetailMapper.selectByPrimaryKey(TEST_ID))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDetailService.findMccGroupDetail(TEST_ID);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(mccGroupDetailMapper).selectByPrimaryKey(TEST_ID);
    }

    /**
     * 测试修改商户类别群详细信息 - 成功场景
     */
    @Test
    void testModifyMccGroupDetail_Success() {
        // Arrange
        when(mccGroupDetailMapper.updateByPrimaryKeySelective(any(ParmMccGroupDetail.class)))
                .thenReturn(1);

        // Act
        Boolean result = mccGroupDetailService.modifyMccGroupDetail(testMccGroupDetailDTO);

        // Assert
        assertTrue(result);
        verify(mccGroupDetailMapper).updateByPrimaryKeySelective(any(ParmMccGroupDetail.class));
    }

    /**
     * 测试根据主键删除商户类别群详细信息 - 成功场景
     */
    @Test
    void testRemoveMccGroupDetail_Success() {
        // Arrange
        when(mccGroupDetailMapper.selectByPrimaryKey(TEST_ID))
                .thenReturn(testParmMccGroupDetail);
        when(mccGroupDetailMapper.deleteByPrimaryKey(TEST_ID))
                .thenReturn(1);

        // Act
        Boolean result = mccGroupDetailService.removeMccGroupDetail(TEST_ID);

        // Assert
        assertTrue(result);
        verify(mccGroupDetailMapper).selectByPrimaryKey(TEST_ID);
        verify(mccGroupDetailMapper).deleteByPrimaryKey(TEST_ID);
    }

    /**
     * 测试新增商户类别群详细信息 - 成功场景
     */
    @Test
    void testAddMccGroupDetail_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testMccGroupDetailDTO, ParmMccGroupDetail.class))
                    .thenReturn(testParmMccGroupDetail);
            
            when(mccGroupDetailSelfMapper.isExists(TEST_MCC_GROUP, TEST_MCC_CODE, TEST_ORGANIZATION_NUMBER))
                    .thenReturn(0);
            when(mccGroupDetailMapper.insertSelective(any(ParmMccGroupDetail.class)))
                    .thenReturn(1);

            // Act
            Boolean result = mccGroupDetailService.addMccGroupDetail(testMccGroupDetailDTO);

            // Assert
            assertTrue(result);
            verify(mccGroupDetailSelfMapper).isExists(TEST_MCC_GROUP, TEST_MCC_CODE, TEST_ORGANIZATION_NUMBER);
            verify(mccGroupDetailMapper).insertSelective(any(ParmMccGroupDetail.class));
        }
    }

    /**
     * 测试新增商户类别群详细信息 - 参数为空
     */
    @Test
    void testAddMccGroupDetail_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDetailService.addMccGroupDetail(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据商户类别码获取列表 - 成功场景
     */
    @Test
    void testGetListByMccCode_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(TEST_ORGANIZATION_NUMBER);
            
            when(mccGroupDetailSelfMapper.selectListByMccCde(TEST_MCC_CODE, TEST_ORGANIZATION_NUMBER))
                    .thenReturn(Arrays.asList(testParmMccGroupDetail));
            
            beanMappingMock.when(() -> BeanMapping.copy(testParmMccGroupDetail, testMccGroupDetailDTO))
                    .then(invocation -> null); // void方法的mock

            // Act
            List<MccGroupDetailDTO> result = mccGroupDetailService.getListByMccCode(TEST_MCC_CODE);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(mccGroupDetailSelfMapper).selectListByMccCde(TEST_MCC_CODE, TEST_ORGANIZATION_NUMBER);
        }
    }

    /**
     * 测试根据商户类别群获取列表 - 成功场景
     */
    @Test
    void testGetListByMccGroup_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(mccGroupDetailSelfMapper.selectListByMccGroup(TEST_MCC_GROUP, TEST_ORGANIZATION_NUMBER))
                    .thenReturn(Arrays.asList(testParmMccGroupDetail));
            
            beanMappingMock.when(() -> BeanMapping.copy(testParmMccGroupDetail, testMccGroupDetailDTO))
                    .then(invocation -> null); // void方法的mock

            // Act
            List<MccGroupDetailDTO> result = mccGroupDetailService.getListByMccGroup(TEST_MCC_GROUP, TEST_ORGANIZATION_NUMBER);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(mccGroupDetailSelfMapper).selectListByMccGroup(TEST_MCC_GROUP, TEST_ORGANIZATION_NUMBER);
        }
    }
} 
