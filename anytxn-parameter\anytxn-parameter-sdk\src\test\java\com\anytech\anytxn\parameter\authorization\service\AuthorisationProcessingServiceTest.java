﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthorisationProcessingMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthorisationProcessingSelfMapper;
import com.anytech.anytxn.parameter.authorization.service.AuthorisationProcessingServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmAuthorisationProcessing;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 授权处理参数服务测试类
 * 
 * <AUTHOR>
 * @date 2024-06-25
 */
@ExtendWith(MockitoExtension.class)
class AuthorisationProcessingServiceTest {

    @Mock
    private ParmAuthorisationProcessingMapper parmAuthorisationProcessingMapper;

    @Mock
    private ParmAuthorisationProcessingSelfMapper parmAuthorisationProcessingSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private AuthorisationProcessingServiceImpl authorisationProcessingService;

    private ParmAuthorisationProcessing mockEntity;
    private AuthorisationProcessingReqDTO mockReqDTO;
    private AuthorisationProcessingResDTO mockResDTO;

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("123456");
            
            // 初始化测试数据
            mockEntity = new ParmAuthorisationProcessing();
            mockEntity.setId("1001");
            mockEntity.setOrganizationNumber("123456");
            mockEntity.setTableId("TABLE001");
            mockEntity.setDescription("测试授权处理参数");
            mockEntity.setAuthorisationRemainDays(30);
            mockEntity.setAuthorisationMatchFlag("Y");
            mockEntity.setAuthorisationMatchAmount(new BigDecimal("100.00"));
            mockEntity.setAuthorisationMatchPer(new BigDecimal("0.05"));
            mockEntity.setStatus("1");
            mockEntity.setAuthMatchData1("DATA1");
            mockEntity.setAuthMatchData2("DATA2");
            mockEntity.setAuthMatchData3("DATA3");
            mockEntity.setAuthMatchData4("DATA4");
            mockEntity.setAuthMatchMethod("1");
            mockEntity.setCreateTime(LocalDateTime.now());
            mockEntity.setUpdateTime(LocalDateTime.now());
            mockEntity.setUpdateBy("test");
            mockEntity.setVersionNumber(1L);

            mockReqDTO = new AuthorisationProcessingReqDTO();
            mockReqDTO.setId("1001");
            mockReqDTO.setOrganizationNumber("123456");
            mockReqDTO.setTableId("TABLE001");
            mockReqDTO.setDescription("测试授权处理参数");
            mockReqDTO.setAuthorisationRemainDays(30);
            mockReqDTO.setAuthorisationMatchFlag("Y");
            mockReqDTO.setAuthorisationMatchAmount(new BigDecimal("100.00"));
            mockReqDTO.setAuthorisationMatchPer(new BigDecimal("0.05"));
            mockReqDTO.setStatus("1");
            mockReqDTO.setAuthMatchData1("DATA1");
            mockReqDTO.setAuthMatchData2("DATA2");
            mockReqDTO.setAuthMatchData3("DATA3");
            mockReqDTO.setAuthMatchData4("DATA4");
            mockReqDTO.setAuthMatchMethod("1");

            mockResDTO = new AuthorisationProcessingResDTO();
            mockResDTO.setId("1001");
            mockResDTO.setOrganizationNumber("123456");
            mockResDTO.setTableId("TABLE001");
            mockResDTO.setDescription("测试授权处理参数");
            mockResDTO.setAuthorisationRemainDays(30);
            mockResDTO.setAuthorisationMatchFlag("Y");
            mockResDTO.setAuthorisationMatchAmount(new BigDecimal("100.00"));
            mockResDTO.setAuthorisationMatchPer(new BigDecimal("0.05"));
            mockResDTO.setStatus("1");
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Given
        String orgNumber = "123456";
        String tableId = "TABLE001";
        
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            when(parmAuthorisationProcessingSelfMapper.selectByOrgAndTableId(orgNumber, tableId))
                    .thenReturn(mockEntity);
            beanMapping.when(() -> BeanMapping.copy(mockEntity, AuthorisationProcessingResDTO.class))
                    .thenReturn(mockResDTO);
            
            // When
            AuthorisationProcessingResDTO result = authorisationProcessingService.findByOrgAndTableId(orgNumber, tableId);
            
            // Then
            assertNotNull(result);
            assertEquals("1001", result.getId());
            assertEquals("TABLE001", result.getTableId());
            assertEquals("测试授权处理参数", result.getDescription());
            verify(parmAuthorisationProcessingSelfMapper).selectByOrgAndTableId(orgNumber, tableId);
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Given
        String orgNumber = "123456";
        String tableId = "TABLE001";
        when(parmAuthorisationProcessingSelfMapper.selectByOrgAndTableId(orgNumber, tableId))
                .thenReturn(null);
        
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.findByOrgAndTableId(orgNumber, tableId));
        verify(parmAuthorisationProcessingSelfMapper).selectByOrgAndTableId(orgNumber, tableId);
    }

    @Test
    void testFindOne_Success() {
        // Given
        String id = "1001";
        
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            when(parmAuthorisationProcessingMapper.selectByPrimaryKey(id))
                    .thenReturn(mockEntity);
            beanMapping.when(() -> BeanMapping.copy(mockEntity, AuthorisationProcessingResDTO.class))
                    .thenReturn(mockResDTO);
            
            // When
            AuthorisationProcessingResDTO result = authorisationProcessingService.findOne(id);
            
            // Then
            assertNotNull(result);
            assertEquals("1001", result.getId());
            assertEquals("TABLE001", result.getTableId());
            verify(parmAuthorisationProcessingMapper).selectByPrimaryKey(id);
        }
    }

    @Test
    void testFindOne_NullId() {
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.findOne(null));
    }

    @Test
    void testFindOne_NotFound() {
        // Given
        String id = "1001";
        when(parmAuthorisationProcessingMapper.selectByPrimaryKey(id))
                .thenReturn(null);
        
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.findOne(id));
        verify(parmAuthorisationProcessingMapper).selectByPrimaryKey(id);
    }

    @Test
    void testFindPage_Success() {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("123456");
            
            List<ParmAuthorisationProcessing> entityList = Arrays.asList(mockEntity);
            when(parmAuthorisationProcessingSelfMapper.selectByCondition(any()))
                    .thenReturn(entityList);
            beanMapping.when(() -> BeanMapping.copyList(entityList, AuthorisationProcessingResDTO.class))
                    .thenReturn(Arrays.asList(mockResDTO));
            
            // When
            PageResultDTO<AuthorisationProcessingResDTO> result = authorisationProcessingService.findPage(
                    1, 10, "TABLE001", "测试", "30", "Y", "100.00", "0.05", "123456");
            
            // Then
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            assertEquals("1001", result.getData().get(0).getId());
        }
    }

    @Test
    void testFindPage_NumberFormatException_AuthorisationRemainDays() {
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.findPage(1, 10, "TABLE001", "测试", "invalid", "Y", "100.00", "0.05", "123456"));
    }

    @Test
    void testFindPage_NumberFormatException_AuthorisationMatchAmount() {
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.findPage(1, 10, "TABLE001", "测试", "30", "Y", "invalid", "0.05", "123456"));
    }

    @Test
    void testFindPage_NumberFormatException_AuthorisationMatchPer() {
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.findPage(1, 10, "TABLE001", "测试", "30", "Y", "100.00", "invalid", "123456"));
    }

    @Test
    void testModify_Success() {
        // Given
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            when(parmAuthorisationProcessingMapper.selectByPrimaryKey(mockReqDTO.getId()))
                    .thenReturn(mockEntity);
            when(parmAuthorisationProcessingSelfMapper.selectByOrgAndTableId(
                    mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId()))
                    .thenReturn(null);
            
            ParmAuthorisationProcessing newEntity = new ParmAuthorisationProcessing();
            beanMapping.when(() -> BeanMapping.copy(mockReqDTO, ParmAuthorisationProcessing.class))
                    .thenReturn(newEntity);
            
            // When
            ParameterCompare result = authorisationProcessingService.modify(mockReqDTO);
            
            // Then
            assertNotNull(result);
            verify(parmAuthorisationProcessingMapper).selectByPrimaryKey(mockReqDTO.getId());
            verify(parmAuthorisationProcessingSelfMapper).selectByOrgAndTableId(
                    mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId());
        }
    }

    @Test
    void testModify_NullId() {
        // Given
        mockReqDTO.setId(null);
        
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.modify(mockReqDTO));
    }

    @Test
    void testModify_RecordNotFound() {
        // Given
        when(parmAuthorisationProcessingMapper.selectByPrimaryKey(mockReqDTO.getId()))
                .thenReturn(null);
        
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.modify(mockReqDTO));
        verify(parmAuthorisationProcessingMapper).selectByPrimaryKey(mockReqDTO.getId());
    }

    @Test
    void testModify_AlreadyExists() {
        // Given
        ParmAuthorisationProcessing existingEntity = new ParmAuthorisationProcessing();
        existingEntity.setId("2002");
        existingEntity.setOrganizationNumber("123456");
        existingEntity.setTableId("TABLE001");
        
        when(parmAuthorisationProcessingMapper.selectByPrimaryKey(mockReqDTO.getId()))
                .thenReturn(mockEntity);
        when(parmAuthorisationProcessingSelfMapper.selectByOrgAndTableId(
                mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId()))
                .thenReturn(existingEntity);
        
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.modify(mockReqDTO));
    }

    @Test
    void testRemove_Success() {
        // Given
        String id = "1001";
        when(parmAuthorisationProcessingMapper.selectByPrimaryKey(id))
                .thenReturn(mockEntity);
        
        // When
        ParameterCompare result = authorisationProcessingService.remove(id);
        
        // Then
        assertNotNull(result);
        verify(parmAuthorisationProcessingMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_NotFound() {
        // Given
        String id = "1001";
        when(parmAuthorisationProcessingMapper.selectByPrimaryKey(id))
                .thenReturn(null);
        
        // When & Then
        assertThrows(AnyTxnParameterException.class,
                () -> authorisationProcessingService.remove(id));
        verify(parmAuthorisationProcessingMapper).selectByPrimaryKey(id);
    }

    @Test
    void testAdd_Success() {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("123456");
            tenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");
            
            when(parmAuthorisationProcessingSelfMapper.isExists(
                    mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId()))
                    .thenReturn(0);
            when(numberIdGenerator.generateId("tenant1")).thenReturn(1001L);
            
            ParmAuthorisationProcessing newEntity = new ParmAuthorisationProcessing();
            beanMapping.when(() -> BeanMapping.copy(mockReqDTO, ParmAuthorisationProcessing.class))
                    .thenReturn(newEntity);
            
            // When
            ParameterCompare result = authorisationProcessingService.add(mockReqDTO);
            
            // Then
            assertNotNull(result);
            verify(parmAuthorisationProcessingSelfMapper).isExists(
                    mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId());
            verify(numberIdGenerator).generateId("tenant1");
        }
    }

    @Test
    void testAdd_AlreadyExists() {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("123456");
            
            when(parmAuthorisationProcessingSelfMapper.isExists(
                    mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId()))
                    .thenReturn(1);
            
            // When & Then
            assertThrows(AnyTxnParameterException.class,
                    () -> authorisationProcessingService.add(mockReqDTO));
            verify(parmAuthorisationProcessingSelfMapper).isExists(
                    mockReqDTO.getOrganizationNumber(), mockReqDTO.getTableId());
        }
    }

    @Test
    void testAdd_NullOrganizationNumber() {
        // Given
        mockReqDTO.setOrganizationNumber(null);
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            
            orgUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("123456");
            tenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");
            
            when(parmAuthorisationProcessingSelfMapper.isExists("123456", mockReqDTO.getTableId()))
                    .thenReturn(0);
            when(numberIdGenerator.generateId("tenant1")).thenReturn(1001L);
            
            ParmAuthorisationProcessing newEntity = new ParmAuthorisationProcessing();
            beanMapping.when(() -> BeanMapping.copy(mockReqDTO, ParmAuthorisationProcessing.class))
                    .thenReturn(newEntity);
            
            // When
            ParameterCompare result = authorisationProcessingService.add(mockReqDTO);
            
            // Then
            assertNotNull(result);
            assertEquals("123456", mockReqDTO.getOrganizationNumber());
            verify(parmAuthorisationProcessingSelfMapper).isExists("123456", mockReqDTO.getTableId());
        }
    }
}
