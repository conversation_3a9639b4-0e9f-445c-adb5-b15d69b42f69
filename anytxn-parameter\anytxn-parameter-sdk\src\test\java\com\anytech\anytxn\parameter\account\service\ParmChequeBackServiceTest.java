﻿package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmChequeBackMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmChequeBackSelfMapper;
import com.anytech.anytxn.parameter.account.service.ChequeBackServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ChequeBackResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmChequeBack;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmChequeBackService单元测试类
 * 测试ChequeBackServiceImpl中的所有公共方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@ExtendWith(MockitoExtension.class)
class ParmChequeBackServiceTest {

    @Mock
    private ParmChequeBackMapper parmChequeBackMapper;

    @Mock
    private ParmChequeBackSelfMapper parmChequeBackSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private ChequeBackServiceImpl chequeBackService;

    private ChequeBackDTO mockChequeBackDTO;
    private ParmChequeBack mockParmChequeBack;
    private ChequeBackResDTO mockChequeBackResDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);

        // 构建测试用的ChequeBackDTO
        mockChequeBackDTO = new ChequeBackDTO();
        mockChequeBackDTO.setId("CHQ001");
        mockChequeBackDTO.setTableId("TABLE001");
        mockChequeBackDTO.setOrganizationNumber("ORG001");
        mockChequeBackDTO.setDescription("测试退票费用");
        mockChequeBackDTO.setFeeIndicator("1");
        mockChequeBackDTO.setStatus("1");
        mockChequeBackDTO.setTransactionCode("TXN001");
        mockChequeBackDTO.setFeeInterestIndicator("1");
        mockChequeBackDTO.setFixedFee(new BigDecimal("100.00"));

        // 构建测试用的ParmChequeBack
        mockParmChequeBack = new ParmChequeBack();
        mockParmChequeBack.setId("CHQ001");
        mockParmChequeBack.setTableId("TABLE001");
        mockParmChequeBack.setOrganizationNumber("ORG001");
        mockParmChequeBack.setDescription("测试退票费用");
        mockParmChequeBack.setFeeIndicator("1");
        mockParmChequeBack.setStatus("1");
        mockParmChequeBack.setTransactionCode("TXN001");
        mockParmChequeBack.setFeeInterestIndicator("1");
        mockParmChequeBack.setFixedFee(new BigDecimal("100.00"));
        mockParmChequeBack.setVersionNumber(1L);
        mockParmChequeBack.setCreateTime(LocalDateTime.now());
        mockParmChequeBack.setUpdateTime(LocalDateTime.now());

        // 构建测试用的ChequeBackResDTO
        mockChequeBackResDTO = new ChequeBackResDTO();
        mockChequeBackResDTO.setId("CHQ001");
        mockChequeBackResDTO.setTableId("TABLE001");
        mockChequeBackResDTO.setOrganizationNumber("ORG001");
        mockChequeBackResDTO.setDescription("测试退票费用");
        mockChequeBackResDTO.setFeeIndicator("1");
        mockChequeBackResDTO.setStatus("1");
        mockChequeBackResDTO.setTransactionCode("TXN001");
        mockChequeBackResDTO.setFeeInterestIndicator("1");
        mockChequeBackResDTO.setFixedFee(new BigDecimal("100.00"));
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 成功路径
     */
    @Test
    void testFindByTableIdAndOrgNo_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String tableId = "TABLE001";
            String orgNo = "ORG001";

            when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(tableId, orgNo))
                    .thenReturn(mockParmChequeBack);
            beanMapping.when(() -> BeanMapping.copy(mockParmChequeBack, ChequeBackResDTO.class))
                    .thenReturn(mockChequeBackResDTO);

            // When
            ChequeBackResDTO result = chequeBackService.findByTableIdAndOrgNo(tableId, orgNo);

            // Then
            assertNotNull(result);
            assertEquals("CHQ001", result.getId());
            assertEquals("TABLE001", result.getTableId());
            verify(parmChequeBackSelfMapper).queryByChequeBackTableIdAndOrgNo(tableId, orgNo);
        }
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - tableId为null异常
     */
    @Test
    void testFindByTableIdAndOrgNo_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.findByTableIdAndOrgNo(null, "ORG001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - organizationNumber为空异常
     */
    @Test
    void testFindByTableIdAndOrgNo_EmptyOrganizationNumber() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.findByTableIdAndOrgNo("TABLE001", "");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 数据未找到异常
     */
    @Test
    void testFindByTableIdAndOrgNo_NotFound() {
        // Given
        String tableId = "TABLE001";
        String orgNo = "ORG001";

        when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(tableId, orgNo))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.findByTableIdAndOrgNo(tableId, orgNo);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add 方法 - 成功路径
     */
    @Test
    void testAdd_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");

            when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(anyString(), anyString()))
                    .thenReturn(null);
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
            beanMapping.when(() -> BeanMapping.copy(mockChequeBackDTO, ParmChequeBack.class))
                    .thenReturn(mockParmChequeBack);

            // When
            ParameterCompare result = chequeBackService.add(mockChequeBackDTO);

            // Then
            assertNotNull(result);
            verify(parmChequeBackSelfMapper).queryByChequeBackTableIdAndOrgNo(anyString(), anyString());
            verify(numberIdGenerator).generateId(anyString());
        }
    }

    /**
     * 测试 add 方法 - 数据已存在异常
     */
    @Test
    void testAdd_AlreadyExists() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            when(parmChequeBackSelfMapper.queryByChequeBackTableIdAndOrgNo(anyString(), anyString()))
                    .thenReturn(mockParmChequeBack);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                chequeBackService.add(mockChequeBackDTO);
            });
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 modify 方法 - 成功路径
     */
    @Test
    void testModify_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            when(parmChequeBackMapper.selectByPrimaryKey(mockChequeBackDTO.getId()))
                    .thenReturn(mockParmChequeBack);
            beanMapping.when(() -> BeanMapping.copy(mockChequeBackDTO, ParmChequeBack.class))
                    .thenReturn(mockParmChequeBack);

            // When
            ParameterCompare result = chequeBackService.modify(mockChequeBackDTO);

            // Then
            assertNotNull(result);
            verify(parmChequeBackMapper).selectByPrimaryKey(mockChequeBackDTO.getId());
        }
    }

    /**
     * 测试 modify 方法 - ID为null异常
     */
    @Test
    void testModify_NullId() {
        // Given
        mockChequeBackDTO.setId(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.modify(mockChequeBackDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 modify 方法 - 数据未找到异常
     */
    @Test
    void testModify_NotFound() {
        // Given
        when(parmChequeBackMapper.selectByPrimaryKey(mockChequeBackDTO.getId()))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.modify(mockChequeBackDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String id) 方法 - 成功路径
     */
    @Test
    void testRemoveById_Success() {
        // Given
        String id = "CHQ001";
        when(parmChequeBackMapper.selectByPrimaryKey(id)).thenReturn(mockParmChequeBack);

        // When
        ParameterCompare result = chequeBackService.remove(id);

        // Then
        assertNotNull(result);
        verify(parmChequeBackMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove(String id) 方法 - ID为null异常
     */
    @Test
    void testRemoveById_NullId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove((String) null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String id) 方法 - 数据未找到异常
     */
    @Test
    void testRemoveById_NotFound() {
        // Given
        String id = "CHQ001";
        when(parmChequeBackMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove(id);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 成功路径
     */
    @Test
    void testRemoveByTableIdAndOrgNum_Success() {
        // Given
        String tableId = "TABLE001";
        String orgNum = "ORG001";
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(mockParmChequeBack);

        // When
        ParameterCompare result = chequeBackService.remove(tableId, orgNum);

        // Then
        assertNotNull(result);
        verify(parmChequeBackMapper).selectByTableIdAndOrgNum(tableId, orgNum);
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - tableId为null异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove(null, "ORG001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 数据未找到异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_NotFound() {
        // Given
        String tableId = "TABLE001";
        String orgNum = "ORG001";
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.remove(tableId, orgNum);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String id) 方法 - 成功路径
     */
    @Test
    void testFindById_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String id = "CHQ001";
            when(parmChequeBackMapper.selectByPrimaryKey(id)).thenReturn(mockParmChequeBack);
            beanMapping.when(() -> BeanMapping.copy(mockParmChequeBack, ChequeBackResDTO.class))
                    .thenReturn(mockChequeBackResDTO);

            // When
            ChequeBackResDTO result = chequeBackService.find(id);

            // Then
            assertNotNull(result);
            assertEquals("CHQ001", result.getId());
            verify(parmChequeBackMapper).selectByPrimaryKey(id);
        }
    }

    /**
     * 测试 find(String id) 方法 - ID为null异常
     */
    @Test
    void testFindById_NullId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find((String) null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String id) 方法 - 数据未找到异常
     */
    @Test
    void testFindById_NotFound() {
        // Given
        String id = "CHQ001";
        when(parmChequeBackMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find(id);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 成功路径
     */
    @Test
    void testFindByTableIdAndOrgNum_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String tableId = "TABLE001";
            String orgNum = "ORG001";
            when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(mockParmChequeBack);
            beanMapping.when(() -> BeanMapping.copy(mockParmChequeBack, ChequeBackResDTO.class))
                    .thenReturn(mockChequeBackResDTO);

            // When
            ChequeBackResDTO result = chequeBackService.find(tableId, orgNum);

            // Then
            assertNotNull(result);
            assertEquals("CHQ001", result.getId());
            verify(parmChequeBackMapper).selectByTableIdAndOrgNum(tableId, orgNum);
        }
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - tableId为null异常
     */
    @Test
    void testFindByTableIdAndOrgNum_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find(null, "ORG001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 数据未找到异常
     */
    @Test
    void testFindByTableIdAndOrgNum_NotFound() {
        // Given
        String tableId = "TABLE001";
        String orgNum = "ORG001";
        when(parmChequeBackMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            chequeBackService.find(tableId, orgNum);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findAll 方法 - 成功路径
     */
    @Test
    void testFindAll_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            Page<ParmChequeBack> mockPage = new Page<>(1, 10);
            mockPage.setTotal(1);
            List<ParmChequeBack> mockList = Arrays.asList(mockParmChequeBack);
            List<ChequeBackResDTO> mockResList = Arrays.asList(mockChequeBackResDTO);

            when(parmChequeBackSelfMapper.selectAll(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, ChequeBackResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<ChequeBackResDTO> result = chequeBackService.findAll(
                    1, 10, "TABLE001", "测试", "1", "1", "TXN001", "1", new BigDecimal("100.00"), "ORG001");

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            verify(parmChequeBackSelfMapper).selectAll(any());
        }
    }

    /**
     * 测试 findAll 方法 - 空organizationNumber使用默认值
     */
    @Test
    void testFindAll_EmptyOrganizationNumber() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            Page<ParmChequeBack> mockPage = new Page<>(1, 10);
            mockPage.setTotal(0);
            List<ParmChequeBack> mockList = Collections.emptyList();
            List<ChequeBackResDTO> mockResList = Collections.emptyList();

            when(parmChequeBackSelfMapper.selectAll(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, ChequeBackResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<ChequeBackResDTO> result = chequeBackService.findAll(
                    1, 10, null, null, null, null, null, null, null, "");

            // Then
            assertNotNull(result);
            verify(parmChequeBackSelfMapper).selectAll(any());
        }
    }

    /**
     * 测试 findByStatus 方法 - 成功路径
     */
    @Test
    void testFindByStatus_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            String status = "1";
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            List<ParmChequeBack> mockList = Arrays.asList(mockParmChequeBack);
            List<ChequeBackResDTO> mockResList = Arrays.asList(mockChequeBackResDTO);

            when(parmChequeBackSelfMapper.selectByStatus(status, "ORG001")).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, ChequeBackResDTO.class))
                    .thenReturn(mockResList);

            // When
            List<ChequeBackResDTO> result = chequeBackService.findByStatus(status);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(parmChequeBackSelfMapper).selectByStatus(status, "ORG001");
        }
    }

    /**
     * 测试 findByStatus 方法 - 空status返回null
     */
    @Test
    void testFindByStatus_EmptyStatus() {
        // When
        List<ChequeBackResDTO> result = chequeBackService.findByStatus("");

        // Then
        assertNull(result);
        verify(parmChequeBackSelfMapper, never()).selectByStatus(anyString(), anyString());
    }

    /**
     * 测试 findByStatus 方法 - null status返回null
     */
    @Test
    void testFindByStatus_NullStatus() {
        // When
        List<ChequeBackResDTO> result = chequeBackService.findByStatus(null);

        // Then
        assertNull(result);
        verify(parmChequeBackSelfMapper, never()).selectByStatus(anyString(), anyString());
    }
}
