﻿package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.account.mapper.ParmQueryFeeMapper;
import com.anytech.anytxn.parameter.account.service.QueryTransFeeTableServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmQueryFeeReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmQueryFeeResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmQueryFeeSearchDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmQueryFee;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * QueryTransFeeTableServiceImpl 单元测试类
 * 测试查询手续费参数服务的所有业务方法
 *
 * <AUTHOR> Assistant
 * @date 2025-06-27
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class QueryTransFeeServiceTest {

    @InjectMocks
    private QueryTransFeeTableServiceImpl queryTransFeeService;

    @Mock
    private ParmQueryFeeMapper parmQueryFeeMapper;

    private ParmQueryFee testParmQueryFee;
    private ParmQueryFeeReqDTO testReqDTO;
    private ParmQueryFeeSearchDTO testSearchDTO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testParmQueryFee = new ParmQueryFee();
        testParmQueryFee.setId("TEST_ID_001");
        testParmQueryFee.setTableId("TEST_TABLE_001");
        testParmQueryFee.setStatus("1");
        testParmQueryFee.setDescription("测试查询手续费");
        testParmQueryFee.setChargeFlag("1");
        testParmQueryFee.setChargeOption("0");
        testParmQueryFee.setFixedAmnt(new BigDecimal("10.00"));
        testParmQueryFee.setFeeMergeFlag("0");
        testParmQueryFee.setTransactionCode("TXN001");
        testParmQueryFee.setOrganizationNumber("ORG001");

        // 使用try-with-resources确保OrgNumberUtils静态Mock生效
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            testReqDTO = new ParmQueryFeeReqDTO();
            testReqDTO.setTableId("TEST_TABLE_001");
            testReqDTO.setDescription("测试查询手续费");
            testReqDTO.setChargeFlag("1");

            testSearchDTO = new ParmQueryFeeSearchDTO();
            testSearchDTO.setTableId("TEST_TABLE_001");
            testSearchDTO.setStatus("1");
        }
    }

    /**
     * 测试方法：testFindAll_ReturnsNull
     * 用来测试 QueryTransFeeTableServiceImpl 方法 findAll
     * 验证分页查询方法返回null（当前为空实现）
     */
    @Test
    void testFindAll_ReturnsNull() {
        // Act
        PageResultDTO<ParmQueryFeeResDTO> result = queryTransFeeService.findAll(1, 10, testSearchDTO);

        // Assert
        assertThat(result).isNull();
    }

    /**
     * 测试方法：testAddParmFee_ReturnsNull
     * 用来测试 QueryTransFeeTableServiceImpl 方法 addParmFee
     * 验证添加查询手续费参数方法返回null（当前为空实现）
     */
    @Test
    void testAddParmFee_ReturnsNull() {
        // Act
        ParameterCompare result = queryTransFeeService.addParmFee(testReqDTO);

        // Assert
        assertThat(result).isNull();
    }

    /**
     * 测试方法：testModifyParmFee_ReturnsNull
     * 用来测试 QueryTransFeeTableServiceImpl 方法 modifyParmFee
     * 验证修改查询手续费参数方法返回null（当前为空实现）
     */
    @Test
    void testModifyParmFee_ReturnsNull() {
        // Act
        ParameterCompare result = queryTransFeeService.modifyParmFee(testReqDTO);

        // Assert
        assertThat(result).isNull();
    }

    /**
     * 测试方法：testRemoveParmFee_ReturnsNull
     * 用来测试 QueryTransFeeTableServiceImpl 方法 removeParmFee
     * 验证删除查询手续费参数方法返回null（当前为空实现）
     */
    @Test
    void testRemoveParmFee_ReturnsNull() {
        // Act
        ParameterCompare result = queryTransFeeService.removeParmFee("TEST_ID_001");

        // Assert
        assertThat(result).isNull();
    }

    /**
     * 测试方法：testFindById_ReturnsNull
     * 用来测试 QueryTransFeeTableServiceImpl 方法 findById
     * 验证根据ID查询方法返回null（当前为空实现）
     */
    @Test
    void testFindById_ReturnsNull() {
        // Act
        ParmQueryFeeResDTO result = queryTransFeeService.findById("TEST_ID_001");

        // Assert
        assertThat(result).isNull();
    }

    /**
     * 测试方法：testFindByOrgAndTableId_Success
     * 用来测试 QueryTransFeeTableServiceImpl 方法 findByOrgAndTableId
     * 验证根据机构号和表ID查询成功的场景
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        when(parmQueryFeeMapper.selectByOrgAndTid("ORG001", "TEST_TABLE_001"))
                .thenReturn(testParmQueryFee);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            ParmQueryFeeResDTO expectedResult = new ParmQueryFeeResDTO();
            expectedResult.setId("TEST_ID_001");
            expectedResult.setTableId("TEST_TABLE_001");
            expectedResult.setDescription("测试查询手续费");

            beanMappingMock.when(() -> BeanMapping.copy(testParmQueryFee, ParmQueryFeeResDTO.class))
                    .thenReturn(expectedResult);

            // Act
            ParmQueryFeeResDTO result = queryTransFeeService.findByOrgAndTableId("ORG001", "TEST_TABLE_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            assertThat(result.getTableId()).isEqualTo("TEST_TABLE_001");
            assertThat(result.getDescription()).isEqualTo("测试查询手续费");

            verify(parmQueryFeeMapper).selectByOrgAndTid("ORG001", "TEST_TABLE_001");
            beanMappingMock.verify(() -> BeanMapping.copy(testParmQueryFee, ParmQueryFeeResDTO.class));
        }
    }

    /**
     * 测试方法：testFindByOrgAndTableId_EmptyParameters
     * 用来测试 QueryTransFeeTableServiceImpl 方法 findByOrgAndTableId
     * 验证当机构号或表ID为空时抛出异常
     */
    @Test
    void testFindByOrgAndTableId_EmptyParameters() {
        // Assert - 机构号为空
        assertThatThrownBy(() -> queryTransFeeService.findByOrgAndTableId("", "TEST_TABLE_001"))
                .isInstanceOf(AnyTxnParameterException.class);

        // Assert - 表ID为空
        assertThatThrownBy(() -> queryTransFeeService.findByOrgAndTableId("ORG001", ""))
                .isInstanceOf(AnyTxnParameterException.class);

        // Assert - 机构号为null
        assertThatThrownBy(() -> queryTransFeeService.findByOrgAndTableId(null, "TEST_TABLE_001"))
                .isInstanceOf(AnyTxnParameterException.class);

        // Assert - 表ID为null
        assertThatThrownBy(() -> queryTransFeeService.findByOrgAndTableId("ORG001", null))
                .isInstanceOf(AnyTxnParameterException.class);
    }

    /**
     * 测试方法：testFindByOrgAndTableId_DataNotFound
     * 用来测试 QueryTransFeeTableServiceImpl 方法 findByOrgAndTableId
     * 验证当查询数据不存在时抛出异常
     */
    @Test
    void testFindByOrgAndTableId_DataNotFound() {
        // Arrange
        when(parmQueryFeeMapper.selectByOrgAndTid("ORG001", "TEST_TABLE_001"))
                .thenReturn(null);

        // Assert
        assertThatThrownBy(() -> queryTransFeeService.findByOrgAndTableId("ORG001", "TEST_TABLE_001"))
                .isInstanceOf(AnyTxnParameterException.class);

        verify(parmQueryFeeMapper).selectByOrgAndTid("ORG001", "TEST_TABLE_001");
    }


} 
