﻿package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmGiroAutoPaymentMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmGiroAutoPaymentSelfMapper;
import com.anytech.anytxn.parameter.account.service.GiroAutoPaymentServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.GiroBackReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.GiroBackResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmGiroAutoPayment;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmGiroAutoPaymentService单元测试类
 * 测试GiroAutoPaymentServiceImpl中的所有公共方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@ExtendWith(MockitoExtension.class)
class ParmGiroAutoPaymentServiceTest {

    @Mock
    private ParmGiroAutoPaymentMapper parmGiroAutoPaymentMapper;

    @Mock
    private ParmGiroAutoPaymentSelfMapper parmGiroAutoPaymentSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private GiroAutoPaymentServiceImpl giroAutoPaymentService;

    private GiroBackReqDTO mockGiroBackReqDTO;
    private ParmGiroAutoPayment mockParmGiroAutoPayment;
    private GiroBackResDTO mockGiroBackResDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);

        // 构建测试用的GiroBackReqDTO
        mockGiroBackReqDTO = new GiroBackReqDTO();
        mockGiroBackReqDTO.setId("GIRO001");
        mockGiroBackReqDTO.setTableId("TABLE001");
        mockGiroBackReqDTO.setOrganizationNumber("ORG001");
        mockGiroBackReqDTO.setDescription("测试GIRO退票费用");
        mockGiroBackReqDTO.setFeeIndicator("1");
        mockGiroBackReqDTO.setStatus("1");
        mockGiroBackReqDTO.setTransactionCode("TXN001");
        mockGiroBackReqDTO.setFeeInterestIndicator("1");
        mockGiroBackReqDTO.setFixedFee(new BigDecimal("50.00"));

        // 构建测试用的ParmGiroAutoPayment
        mockParmGiroAutoPayment = new ParmGiroAutoPayment();
        mockParmGiroAutoPayment.setId("GIRO001");
        mockParmGiroAutoPayment.setTableId("TABLE001");
        mockParmGiroAutoPayment.setOrganizationNumber("ORG001");
        mockParmGiroAutoPayment.setDescription("测试GIRO退票费用");
        mockParmGiroAutoPayment.setFeeIndicator("1");
        mockParmGiroAutoPayment.setStatus("1");
        mockParmGiroAutoPayment.setTransactionCode("TXN001");
        mockParmGiroAutoPayment.setFeeInterestIndicator("1");
        mockParmGiroAutoPayment.setFixedFee(new BigDecimal("50.00"));
        mockParmGiroAutoPayment.setVersionNumber(1L);
        mockParmGiroAutoPayment.setCreateTime(LocalDateTime.now());
        mockParmGiroAutoPayment.setUpdateTime(LocalDateTime.now());

        // 构建测试用的GiroBackResDTO
        mockGiroBackResDTO = new GiroBackResDTO();
        mockGiroBackResDTO.setId("GIRO001");
        mockGiroBackResDTO.setTableId("TABLE001");
        mockGiroBackResDTO.setOrganizationNumber("ORG001");
        mockGiroBackResDTO.setDescription("测试GIRO退票费用");
        mockGiroBackResDTO.setFeeIndicator("1");
        mockGiroBackResDTO.setStatus("1");
        mockGiroBackResDTO.setTransactionCode("TXN001");
        mockGiroBackResDTO.setFeeInterestIndicator("1");
        mockGiroBackResDTO.setFixedFee(new BigDecimal("50.00"));
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 成功路径
     */
    @Test
    void testFindByTableIdAndOrgNo_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String tableId = "TABLE001";
            String orgNo = "ORG001";

            when(parmGiroAutoPaymentSelfMapper.queryByGiroBackTableIdAndOrgNo(tableId, orgNo))
                    .thenReturn(mockParmGiroAutoPayment);
            beanMapping.when(() -> BeanMapping.copy(mockParmGiroAutoPayment, GiroBackResDTO.class))
                    .thenReturn(mockGiroBackResDTO);

            // When
            GiroBackResDTO result = giroAutoPaymentService.findByTableIdAndOrgNo(tableId, orgNo);

            // Then
            assertNotNull(result);
            assertEquals("GIRO001", result.getId());
            assertEquals("TABLE001", result.getTableId());
            verify(parmGiroAutoPaymentSelfMapper).queryByGiroBackTableIdAndOrgNo(tableId, orgNo);
        }
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - tableId为null异常
     */
    @Test
    void testFindByTableIdAndOrgNo_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.findByTableIdAndOrgNo(null, "ORG001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - organizationNumber为空异常
     */
    @Test
    void testFindByTableIdAndOrgNo_EmptyOrganizationNumber() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.findByTableIdAndOrgNo("TABLE001", "");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByTableIdAndOrgNo 方法 - 数据未找到异常
     */
    @Test
    void testFindByTableIdAndOrgNo_NotFound() {
        // Given
        String tableId = "TABLE001";
        String orgNo = "ORG001";

        when(parmGiroAutoPaymentSelfMapper.queryByGiroBackTableIdAndOrgNo(tableId, orgNo))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.findByTableIdAndOrgNo(tableId, orgNo);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add 方法 - 成功路径
     */
    @Test
    void testAdd_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");

            when(parmGiroAutoPaymentSelfMapper.queryByGiroBackTableIdAndOrgNo(anyString(), anyString()))
                    .thenReturn(null);
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
            beanMapping.when(() -> BeanMapping.copy(mockGiroBackReqDTO, ParmGiroAutoPayment.class))
                    .thenReturn(mockParmGiroAutoPayment);

            // When
            ParameterCompare result = giroAutoPaymentService.add(mockGiroBackReqDTO);

            // Then
            assertNotNull(result);
            verify(parmGiroAutoPaymentSelfMapper).queryByGiroBackTableIdAndOrgNo(anyString(), anyString());
            verify(numberIdGenerator).generateId(anyString());
        }
    }

    /**
     * 测试 add 方法 - 数据已存在异常
     */
    @Test
    void testAdd_AlreadyExists() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            when(parmGiroAutoPaymentSelfMapper.queryByGiroBackTableIdAndOrgNo(anyString(), anyString()))
                    .thenReturn(mockParmGiroAutoPayment);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                giroAutoPaymentService.add(mockGiroBackReqDTO);
            });
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 modify 方法 - 成功路径
     */
    @Test
    void testModify_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            when(parmGiroAutoPaymentMapper.selectByPrimaryKey(mockGiroBackReqDTO.getId()))
                    .thenReturn(mockParmGiroAutoPayment);
            beanMapping.when(() -> BeanMapping.copy(mockGiroBackReqDTO, ParmGiroAutoPayment.class))
                    .thenReturn(mockParmGiroAutoPayment);

            // When
            ParameterCompare result = giroAutoPaymentService.modify(mockGiroBackReqDTO);

            // Then
            assertNotNull(result);
            verify(parmGiroAutoPaymentMapper).selectByPrimaryKey(mockGiroBackReqDTO.getId());
        }
    }

    /**
     * 测试 modify 方法 - ID为null异常
     */
    @Test
    void testModify_NullId() {
        // Given
        mockGiroBackReqDTO.setId(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.modify(mockGiroBackReqDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 modify 方法 - 数据未找到异常
     */
    @Test
    void testModify_NotFound() {
        // Given
        when(parmGiroAutoPaymentMapper.selectByPrimaryKey(mockGiroBackReqDTO.getId()))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.modify(mockGiroBackReqDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_MODIFY_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String id) 方法 - 成功路径
     */
    @Test
    void testRemoveById_Success() {
        // Given
        String id = "GIRO001";
        when(parmGiroAutoPaymentMapper.selectByPrimaryKey(id)).thenReturn(mockParmGiroAutoPayment);

        // When
        ParameterCompare result = giroAutoPaymentService.remove(id);

        // Then
        assertNotNull(result);
        verify(parmGiroAutoPaymentMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove(String id) 方法 - ID为null异常
     */
    @Test
    void testRemoveById_NullId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.remove((String) null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String id) 方法 - 数据未找到异常
     */
    @Test
    void testRemoveById_NotFound() {
        // Given
        String id = "GIRO001";
        when(parmGiroAutoPaymentMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.remove(id);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 成功路径
     */
    @Test
    void testRemoveByTableIdAndOrgNum_Success() {
        // Given
        String tableId = "TABLE001";
        String orgNum = "ORG001";
        when(parmGiroAutoPaymentMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(mockParmGiroAutoPayment);

        // When
        ParameterCompare result = giroAutoPaymentService.remove(tableId, orgNum);

        // Then
        assertNotNull(result);
        verify(parmGiroAutoPaymentMapper).selectByTableIdAndOrgNum(tableId, orgNum);
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - tableId为null异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.remove(null, "ORG001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - orgNum为null异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_NullOrgNum() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.remove("TABLE001", null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_DELETE_EMPTY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove(String tableId, String orgNum) 方法 - 数据未找到异常
     */
    @Test
    void testRemoveByTableIdAndOrgNum_NotFound() {
        // Given
        String tableId = "TABLE001";
        String orgNum = "ORG001";
        when(parmGiroAutoPaymentMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.remove(tableId, orgNum);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_PARA_AUTO_PAYMENT_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String id) 方法 - 成功路径
     */
    @Test
    void testFindById_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String id = "GIRO001";
            when(parmGiroAutoPaymentMapper.selectByPrimaryKey(id)).thenReturn(mockParmGiroAutoPayment);
            beanMapping.when(() -> BeanMapping.copy(mockParmGiroAutoPayment, GiroBackResDTO.class))
                    .thenReturn(mockGiroBackResDTO);

            // When
            GiroBackResDTO result = giroAutoPaymentService.find(id);

            // Then
            assertNotNull(result);
            assertEquals("GIRO001", result.getId());
            verify(parmGiroAutoPaymentMapper).selectByPrimaryKey(id);
        }
    }

    /**
     * 测试 find(String id) 方法 - ID为null异常
     */
    @Test
    void testFindById_NullId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.find((String) null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String id) 方法 - 数据未找到异常
     */
    @Test
    void testFindById_NotFound() {
        // Given
        String id = "GIRO001";
        when(parmGiroAutoPaymentMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.find(id);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 成功路径
     */
    @Test
    void testFindByTableIdAndOrgNum_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String tableId = "TABLE001";
            String orgNum = "ORG001";
            when(parmGiroAutoPaymentMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(mockParmGiroAutoPayment);
            beanMapping.when(() -> BeanMapping.copy(mockParmGiroAutoPayment, GiroBackResDTO.class))
                    .thenReturn(mockGiroBackResDTO);

            // When
            GiroBackResDTO result = giroAutoPaymentService.find(tableId, orgNum);

            // Then
            assertNotNull(result);
            assertEquals("GIRO001", result.getId());
            verify(parmGiroAutoPaymentMapper).selectByTableIdAndOrgNum(tableId, orgNum);
        }
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - tableId为null异常
     */
    @Test
    void testFindByTableIdAndOrgNum_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.find(null, "ORG001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - orgNum为null异常
     */
    @Test
    void testFindByTableIdAndOrgNum_NullOrgNum() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.find("TABLE001", null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 find(String tableId, String orgNum) 方法 - 数据未找到异常
     */
    @Test
    void testFindByTableIdAndOrgNum_NotFound() {
        // Given
        String tableId = "TABLE001";
        String orgNum = "ORG001";
        when(parmGiroAutoPaymentMapper.selectByTableIdAndOrgNum(tableId, orgNum)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            giroAutoPaymentService.find(tableId, orgNum);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARA_AUTO_PAYMENT_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findAll 方法 - 成功路径
     */
    @Test
    void testFindAll_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            Page<ParmGiroAutoPayment> mockPage = new Page<>(1, 10);
            mockPage.setTotal(1);
            List<ParmGiroAutoPayment> mockList = Arrays.asList(mockParmGiroAutoPayment);
            List<GiroBackResDTO> mockResList = Arrays.asList(mockGiroBackResDTO);

            when(parmGiroAutoPaymentSelfMapper.selectAll(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, GiroBackResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<GiroBackResDTO> result = giroAutoPaymentService.findAll(
                    1, 10, "TABLE001", "测试", "1", "1", "TXN001", "1", new BigDecimal("50.00"), "ORG001");

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            verify(parmGiroAutoPaymentSelfMapper).selectAll(any());
        }
    }

    /**
     * 测试 findAll 方法 - 空organizationNumber使用默认值
     */
    @Test
    void testFindAll_EmptyOrganizationNumber() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            Page<ParmGiroAutoPayment> mockPage = new Page<>(1, 10);
            mockPage.setTotal(0);
            List<ParmGiroAutoPayment> mockList = Collections.emptyList();
            List<GiroBackResDTO> mockResList = Collections.emptyList();

            when(parmGiroAutoPaymentSelfMapper.selectAll(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, GiroBackResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<GiroBackResDTO> result = giroAutoPaymentService.findAll(
                    1, 10, null, null, null, null, null, null, null, "");

            // Then
            assertNotNull(result);
            verify(parmGiroAutoPaymentSelfMapper).selectAll(any());
        }
    }

    /**
     * 测试 findByStatus 方法 - 成功路径
     */
    @Test
    void testFindByStatus_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            String status = "1";
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            List<ParmGiroAutoPayment> mockList = Arrays.asList(mockParmGiroAutoPayment);
            List<GiroBackResDTO> mockResList = Arrays.asList(mockGiroBackResDTO);

            when(parmGiroAutoPaymentSelfMapper.selectByStatus(status, "ORG001")).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, GiroBackResDTO.class))
                    .thenReturn(mockResList);

            // When
            List<GiroBackResDTO> result = giroAutoPaymentService.findByStatus(status);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(parmGiroAutoPaymentSelfMapper).selectByStatus(status, "ORG001");
        }
    }

    /**
     * 测试 findByStatus 方法 - 空status返回null
     */
    @Test
    void testFindByStatus_EmptyStatus() {
        // When
        List<GiroBackResDTO> result = giroAutoPaymentService.findByStatus("");

        // Then
        assertNull(result);
        verify(parmGiroAutoPaymentSelfMapper, never()).selectByStatus(anyString(), anyString());
    }

    /**
     * 测试 findByStatus 方法 - null status返回null
     */
    @Test
    void testFindByStatus_NullStatus() {
        // When
        List<GiroBackResDTO> result = giroAutoPaymentService.findByStatus(null);

        // Then
        assertNull(result);
        verify(parmGiroAutoPaymentSelfMapper, never()).selectByStatus(anyString(), anyString());
    }
} 
