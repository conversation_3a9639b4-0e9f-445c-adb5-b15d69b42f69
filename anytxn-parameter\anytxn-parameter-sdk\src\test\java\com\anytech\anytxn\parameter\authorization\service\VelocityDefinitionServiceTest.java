﻿package com.anytech.anytxn.parameter.authorization.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionSelfMapper;
import com.anytech.anytxn.parameter.authorization.service.VelocityDefinitionServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityDefinition;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;

/**
 * VelocityDefinitionService的单元测试类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
class VelocityDefinitionServiceTest {

    @Mock
    private ParmVelocityDefinitionMapper parmVelocityDefinitionMapper;

    @Mock
    private ParmVelocityDefinitionSelfMapper parmVelocityDefinitionSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private VelocityDefinitionServiceImpl velocityDefinitionService;

    private String testOrganizationNumber;
    private String testVelocityCode;
    private String testId;
    private BigDecimal testMaxTxnAmount;
    private Long testMaxTxnCount;
    private String testDescription;
    private String testPeriodUnit;
    private String testStatus;
    private ParmVelocityDefinition testParmVelocityDefinition;
    private VelocityDefinitionDTO testVelocityDefinitionDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 通过反射初始化OrgNumberUtils的静态实例
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        orgNumberUtilField.set(null, new OrgNumberUtils());
        
        // 初始化测试数据
        testOrganizationNumber = "001";
        testVelocityCode = "VEL001";
        testId = "123456789";
        testMaxTxnAmount = new BigDecimal("10000.00");
        testMaxTxnCount = 100L;
        testDescription = "测试流量检查定义";
        testPeriodUnit = "D";
        testStatus = "1";

        // 初始化ParmVelocityDefinition
        testParmVelocityDefinition = new ParmVelocityDefinition();
        testParmVelocityDefinition.setId(testId);
        testParmVelocityDefinition.setOrganizationNumber(testOrganizationNumber);
        testParmVelocityDefinition.setVelocityCde(testVelocityCode);
        testParmVelocityDefinition.setDescription(testDescription);
        testParmVelocityDefinition.setPeriodUnit(testPeriodUnit);
        testParmVelocityDefinition.setMaxTxnCount(testMaxTxnCount);
        testParmVelocityDefinition.setMaxTxnAmount(testMaxTxnAmount);
        testParmVelocityDefinition.setStatus(testStatus);

        // 初始化VelocityDefinitionDTO
        testVelocityDefinitionDTO = new VelocityDefinitionDTO();
        testVelocityDefinitionDTO.setId(testId);
        testVelocityDefinitionDTO.setOrganizationNumber(testOrganizationNumber);
        testVelocityDefinitionDTO.setVelocityCde(testVelocityCode);
        testVelocityDefinitionDTO.setDescription(testDescription);
        testVelocityDefinitionDTO.setPeriodUnit(testPeriodUnit);
        testVelocityDefinitionDTO.setMaxTxnCount(testMaxTxnCount);
        testVelocityDefinitionDTO.setMaxTxnAmount(testMaxTxnAmount);
        testVelocityDefinitionDTO.setStatus(testStatus);
    }

    /**
     * 测试分页查询流量检查定义 - 成功场景
     */
    @Test
    void testFindListVelocityDefinition_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocityDefinitionSelfMapper.selectByCondition(any(VelocityDefinitionDTO.class)))
                .thenReturn(Arrays.asList(testParmVelocityDefinition));

            // Act
            PageResultDTO<VelocityDefinitionDTO> result = velocityDefinitionService.findListVelocityDefinition(1, 10, testVelocityDefinitionDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals(testVelocityCode, result.getData().get(0).getVelocityCde());
            verify(parmVelocityDefinitionSelfMapper).selectByCondition(any(VelocityDefinitionDTO.class));
        }
    }

    /**
     * 测试分页查询流量检查定义 - 空参数使用默认值
     */
    @Test
    void testFindListVelocityDefinition_NullParameter() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocityDefinitionSelfMapper.selectByCondition(any(VelocityDefinitionDTO.class)))
                .thenReturn(Arrays.asList(testParmVelocityDefinition));

            // Act
            PageResultDTO<VelocityDefinitionDTO> result = velocityDefinitionService.findListVelocityDefinition(1, 10, null);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getData().size());
            verify(parmVelocityDefinitionSelfMapper).selectByCondition(any(VelocityDefinitionDTO.class));
        }
    }

    /**
     * 测试分页查询流量检查定义 - 数据库异常
     */
    @Test
    void testFindListVelocityDefinition_DatabaseException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocityDefinitionSelfMapper.selectByCondition(any(VelocityDefinitionDTO.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                velocityDefinitionService.findListVelocityDefinition(1, 10, testVelocityDefinitionDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_VELOCITY_DEFINITION_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试根据主键查询流量检查定义 - 成功场景
     */
    @Test
    void testFindVelocityDefinition_Success() {
        // Arrange
        when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(testParmVelocityDefinition);

        // Act
        VelocityDefinitionDTO result = velocityDefinitionService.findVelocityDefinition(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testVelocityCode, result.getVelocityCde());
        assertEquals(testDescription, result.getDescription());
        verify(parmVelocityDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据主键查询流量检查定义 - 记录不存在
     */
    @Test
    void testFindVelocityDefinition_RecordNotFound() {
        // Arrange
        when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.findVelocityDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据主键查询流量检查定义 - 数据库异常
     */
    @Test
    void testFindVelocityDefinition_DatabaseException() {
        // Arrange
        when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.findVelocityDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改流量检查定义 - 成功场景
     */
    @Test
    void testModifyVelocityDefinition_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(testParmVelocityDefinition);
            
            beanMappingMock.when(() -> BeanMapping.copy(testVelocityDefinitionDTO, ParmVelocityDefinition.class))
                .thenReturn(testParmVelocityDefinition);

            // Act
            ParameterCompare result = velocityDefinitionService.modifyVelocityDefinition(testVelocityDefinitionDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testVelocityCode, result.getMainParmId());
            verify(parmVelocityDefinitionMapper).selectByPrimaryKey(testId);
        }
    }

    /**
     * 测试修改流量检查定义 - ID为空
     */
    @Test
    void testModifyVelocityDefinition_NullId() {
        // Arrange
        testVelocityDefinitionDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.modifyVelocityDefinition(testVelocityDefinitionDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改流量检查定义 - 记录不存在
     */
    @Test
    void testModifyVelocityDefinition_RecordNotFound() {
        // Arrange
        when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.modifyVelocityDefinition(testVelocityDefinitionDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除流量检查定义 - 成功场景
     */
    @Test
    void testRemoveVelocityDefinition_Success() {
        // Arrange
        when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(testParmVelocityDefinition);

        // Act
        ParameterCompare result = velocityDefinitionService.removeVelocityDefinition(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testVelocityCode, result.getMainParmId());
        verify(parmVelocityDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试删除流量检查定义 - 记录不存在
     */
    @Test
    void testRemoveVelocityDefinition_RecordNotFound() {
        // Arrange
        when(parmVelocityDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.removeVelocityDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增流量检查定义 - 成功场景
     */
    @Test
    void testAddVelocityDefinition_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(testVelocityDefinitionDTO, ParmVelocityDefinition.class))
                .thenReturn(testParmVelocityDefinition);
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(numberIdGenerator.generateId("tenant001")).thenReturn(Long.valueOf(testId));
            
            when(parmVelocityDefinitionSelfMapper.isExistsByVelocityCde(testOrganizationNumber, testVelocityCode))
                .thenReturn(0);

            // Act
            ParameterCompare result = velocityDefinitionService.addVelocityDefinition(testVelocityDefinitionDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testVelocityCode, result.getMainParmId());
            verify(parmVelocityDefinitionSelfMapper).isExistsByVelocityCde(testOrganizationNumber, testVelocityCode);
        }
    }

    /**
     * 测试新增流量检查定义 - 参数为空
     */
    @Test
    void testAddVelocityDefinition_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.addVelocityDefinition(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增流量检查定义 - 记录已存在
     */
    @Test
    void testAddVelocityDefinition_RecordExists() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testVelocityDefinitionDTO, ParmVelocityDefinition.class))
                .thenReturn(testParmVelocityDefinition);
            
            when(parmVelocityDefinitionSelfMapper.isExistsByVelocityCde(testOrganizationNumber, testVelocityCode))
                .thenReturn(1);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                velocityDefinitionService.addVelocityDefinition(testVelocityDefinitionDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_DEFINITION_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试根据速度码查询流量检查定义 - 成功场景
     */
    @Test
    void testGetVelocityByCode_Success() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectByVelocityCode(testOrganizationNumber, testVelocityCode))
            .thenReturn(testParmVelocityDefinition);

        // Act
        VelocityDefinitionDTO result = velocityDefinitionService.getVelocityByCode(testOrganizationNumber, testVelocityCode);

        // Assert
        assertNotNull(result);
        assertEquals(testVelocityCode, result.getVelocityCde());
        assertEquals(testDescription, result.getDescription());
        verify(parmVelocityDefinitionSelfMapper).selectByVelocityCode(testOrganizationNumber, testVelocityCode);
    }

    /**
     * 测试根据速度码查询流量检查定义 - 记录不存在
     */
    @Test
    void testGetVelocityByCode_RecordNotFound() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectByVelocityCode(testOrganizationNumber, testVelocityCode))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.getVelocityByCode(testOrganizationNumber, testVelocityCode);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_NULL_BY_VELOCITY_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据速度码查询流量检查定义 - 数据库异常
     */
    @Test
    void testGetVelocityByCode_DatabaseException() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectByVelocityCode(testOrganizationNumber, testVelocityCode))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityDefinitionService.getVelocityByCode(testOrganizationNumber, testVelocityCode);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_DEFINITION_BY_VELOCITY_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试获取所有流量检查码 - 成功场景
     */
    @Test
    void testGetVelocityCodes_Success() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectAll(true, testOrganizationNumber))
            .thenReturn(Arrays.asList(testParmVelocityDefinition));

        // Act
        List<Map<String, String>> result = velocityDefinitionService.getVelocityCodes(testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        Map<String, String> map = result.get(0);
        assertEquals(testVelocityCode, map.get("value"));
        assertEquals(testDescription, map.get("label"));
        verify(parmVelocityDefinitionSelfMapper).selectAll(true, testOrganizationNumber);
    }

    /**
     * 测试获取所有流量检查码 - 空列表
     */
    @Test
    void testGetVelocityCodes_EmptyList() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectAll(true, testOrganizationNumber))
            .thenReturn(Collections.emptyList());

        // Act
        List<Map<String, String>> result = velocityDefinitionService.getVelocityCodes(testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
        verify(parmVelocityDefinitionSelfMapper).selectAll(true, testOrganizationNumber);
    }

    /**
     * 测试获取所有流量检查码 - 多条记录
     */
    @Test
    void testGetVelocityCodes_MultipleRecords() {
        // Arrange
        ParmVelocityDefinition secondVelocityDefinition = new ParmVelocityDefinition();
        secondVelocityDefinition.setVelocityCde("VEL002");
        secondVelocityDefinition.setDescription("第二个流量检查定义");
        
        when(parmVelocityDefinitionSelfMapper.selectAll(true, testOrganizationNumber))
            .thenReturn(Arrays.asList(testParmVelocityDefinition, secondVelocityDefinition));

        // Act
        List<Map<String, String>> result = velocityDefinitionService.getVelocityCodes(testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        
        Map<String, String> firstMap = result.get(0);
        assertEquals(testVelocityCode, firstMap.get("value"));
        assertEquals(testDescription, firstMap.get("label"));
        
        Map<String, String> secondMap = result.get(1);
        assertEquals("VEL002", secondMap.get("value"));
        assertEquals("第二个流量检查定义", secondMap.get("label"));
        
        verify(parmVelocityDefinitionSelfMapper).selectAll(true, testOrganizationNumber);
    }
} 
