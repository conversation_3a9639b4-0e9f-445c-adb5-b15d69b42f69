﻿package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.account.service.InterestHisServiceImpl;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisMapper;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestHisResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.PmInterestHis;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InterestHisServiceTest
 * 测试类：InterestHisServiceImpl
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class InterestHisServiceTest {

    @Mock
    private PmInterestHisMapper pmInterestHisMapper;

    @Mock
    private PmInterestHisSelfMapper pmInterestHisSelfMapper;

    @InjectMocks
    private InterestHisServiceImpl interestHisService;

    private PmInterestHis testPmInterestHis;
    private InterestHisResDTO testInterestHisResDTO;

    @BeforeEach
    void setUp() {
        // 使用try-with-resources处理OrgNumberUtils静态Mock，确保在创建BaseParam对象时生效
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // 创建测试数据
            testPmInterestHis = createTestPmInterestHis();
            testInterestHisResDTO = createTestInterestHisResDTO();
        }
    }

    private PmInterestHis createTestPmInterestHis() {
        PmInterestHis pmInterestHis = new PmInterestHis();
        pmInterestHis.setId(1L);
        pmInterestHis.setOrganizationNumber("ORG001");
        pmInterestHis.setInterestTableId("TABLE001");
        pmInterestHis.setOperateDate(LocalDate.now());
        pmInterestHis.setOperateType("I");
        pmInterestHis.setStatus("1");
        pmInterestHis.setCreateTime(LocalDateTime.now());
        pmInterestHis.setUpdateTime(LocalDateTime.now());
        pmInterestHis.setUpdateBy("TEST_USER");
        pmInterestHis.setVersionNumber(1L);
        
        // 创建JSON值
        InterestResDTO interestResDTO = new InterestResDTO();
        interestResDTO.setDescription("测试利率参数");
        interestResDTO.setInterestType("0");
        interestResDTO.setAdjustRate(new BigDecimal("0.05"));
        interestResDTO.setAdjustRatePercent(new BigDecimal("5.00"));
        interestResDTO.setBaseRate(new BigDecimal("0.03"));
        interestResDTO.setBaseRatePercent(new BigDecimal("3.00"));
        interestResDTO.setGraceOption("Y");
        interestResDTO.setInterestOnInterestOption("N");
        interestResDTO.setMonthBase("360");
        interestResDTO.setYearBase("365");
        interestResDTO.setPaymentBackdateOption("Y");
        interestResDTO.setStartDateOption("1");
        interestResDTO.setWavieOption("N");
        interestResDTO.setInterestBillingTxnCode("INT001");
        interestResDTO.setInterestBackDateDays(30);
        interestResDTO.setLmtUnitCodeFollowIndicator("Y");
        interestResDTO.setCreditIntPostTxnCde("CREDIT001");
        interestResDTO.setCreditIntCalType("1");
        interestResDTO.setIntTaxPostTxnCde("TAX001");
        interestResDTO.setIntTaxRate(new BigDecimal("0.20"));
        interestResDTO.setPaymentRestoreBackdateOption(1);
        interestResDTO.setPaymentRestoreBackdateDays(15);
        
        pmInterestHis.setJsonValue(JSONObject.toJSONString(interestResDTO));
        return pmInterestHis;
    }

    private InterestHisResDTO createTestInterestHisResDTO() {
        InterestHisResDTO interestHisResDTO = new InterestHisResDTO();
        interestHisResDTO.setId(1L);
        interestHisResDTO.setInterestTableId("TABLE001");
        interestHisResDTO.setOperateDate(LocalDate.now());
        interestHisResDTO.setOperateType("I");
        interestHisResDTO.setDescription("测试利率参数");
        interestHisResDTO.setInterestType("0");
        interestHisResDTO.setAdjustRate(new BigDecimal("0.05"));
        interestHisResDTO.setAdjustRatePercent(new BigDecimal("5.00"));
        interestHisResDTO.setBaseRate(new BigDecimal("0.03"));
        interestHisResDTO.setBaseRatePercent(new BigDecimal("3.00"));
        interestHisResDTO.setGraceOption("Y");
        interestHisResDTO.setInterestOnInterestOption("N");
        interestHisResDTO.setMonthBase("360");
        interestHisResDTO.setYearBase("365");
        interestHisResDTO.setPaymentBackdateOption("Y");
        interestHisResDTO.setStartDateOption("1");
        interestHisResDTO.setWavieOption("N");
        interestHisResDTO.setInterestBillingTxnCode("INT001");
        interestHisResDTO.setInterestBackDateDays(30);
        interestHisResDTO.setLmtUnitCodeFollowIndicator("Y");
        interestHisResDTO.setCreditIntPostTxnCde("CREDIT001");
        interestHisResDTO.setCreditIntCalType("1");
        interestHisResDTO.setIntTaxPostTxnCde("TAX001");
        interestHisResDTO.setIntTaxRate(new BigDecimal("0.20"));
        interestHisResDTO.setPaymentRestoreBackdateOption(1);
        interestHisResDTO.setPaymentRestoreBackdateDays(15);
        return interestHisResDTO;
    }

    /**
     * 测试方法：testFindByOrgAndInterestTableId_Success
     * 用来测试 InterestHisServiceImpl 方法 findByOrgAndInterestTableId
     * 验证根据机构号和利率表ID查询利率历史成功的场景
     */
    @Test
    void testFindByOrgAndInterestTableId_Success() {
        // Arrange
        List<PmInterestHis> mockList = new ArrayList<>();
        mockList.add(testPmInterestHis);
        
        when(pmInterestHisSelfMapper.selectByOrgAndInterestTableId("ORG001", "TABLE001"))
                .thenReturn(mockList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            beanMappingMock.when(() -> BeanMapping.copy(any(PmInterestHis.class), eq(InterestHisResDTO.class)))
                    .thenReturn(testInterestHisResDTO);

            // Act
            List<InterestHisResDTO> result = interestHisService.findByOrgAndInterestTableId("ORG001", "TABLE001");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("TABLE001", result.get(0).getInterestTableId());
            assertEquals("测试利率参数", result.get(0).getDescription());
            assertEquals("0", result.get(0).getInterestType());
            
            verify(pmInterestHisSelfMapper).selectByOrgAndInterestTableId("ORG001", "TABLE001");
        }
    }

    /**
     * 测试方法：testFindByOrgAndInterestTableId_EmptyResult
     * 用来测试 InterestHisServiceImpl 方法 findByOrgAndInterestTableId
     * 验证查询结果为空的场景
     */
    @Test
    void testFindByOrgAndInterestTableId_EmptyResult() {
        // Arrange
        when(pmInterestHisSelfMapper.selectByOrgAndInterestTableId("ORG001", "TABLE001"))
                .thenReturn(Collections.emptyList());

        // Act
        List<InterestHisResDTO> result = interestHisService.findByOrgAndInterestTableId("ORG001", "TABLE001");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(pmInterestHisSelfMapper).selectByOrgAndInterestTableId("ORG001", "TABLE001");
    }

    /**
     * 测试方法：testFindPageByOrgAndInterestTableId_Success
     * 用来测试 InterestHisServiceImpl 方法 findPageByOrgAndInterestTableId
     * 验证分页查询利率历史成功的场景
     */
    @Test
    void testFindPageByOrgAndInterestTableId_Success() {
        // Arrange
        List<PmInterestHis> mockList = new ArrayList<>();
        mockList.add(testPmInterestHis);
        
        when(pmInterestHisSelfMapper.selectByCondition("ORG001", "TABLE001"))
                .thenReturn(mockList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            beanMappingMock.when(() -> BeanMapping.copy(any(PmInterestHis.class), eq(InterestHisResDTO.class)))
                    .thenReturn(testInterestHisResDTO);

            // Act
            PageResultDTO<InterestHisResDTO> result = interestHisService.findPageByOrgAndInterestTableId(1, 10, "ORG001", "TABLE001");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            assertEquals("TABLE001", result.getData().get(0).getInterestTableId());
            
            verify(pmInterestHisSelfMapper).selectByCondition("ORG001", "TABLE001");
        }
    }

    /**
     * 测试方法：testFindPageByOrgAndInterestTableId_EmptyResult
     * 用来测试 InterestHisServiceImpl 方法 findPageByOrgAndInterestTableId
     * 验证分页查询结果为空的场景
     */
    @Test
    void testFindPageByOrgAndInterestTableId_EmptyResult() {
        // Arrange
        when(pmInterestHisSelfMapper.selectByCondition("ORG001", "TABLE001"))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // Act
            PageResultDTO<InterestHisResDTO> result = interestHisService.findPageByOrgAndInterestTableId(1, 10, "ORG001", "TABLE001");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertNotNull(result.getData());
            assertTrue(result.getData().isEmpty());
            
            verify(pmInterestHisSelfMapper).selectByCondition("ORG001", "TABLE001");
        }
    }

    /**
     * 测试方法：testFindById_Success
     * 用来测试 InterestHisServiceImpl 方法 findById
     * 验证根据ID查询利率历史成功的场景
     */
    @Test
    void testFindById_Success() {
        // Arrange
        when(pmInterestHisMapper.selectByPrimaryKey(1L)).thenReturn(testPmInterestHis);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            beanMappingMock.when(() -> BeanMapping.copy(any(PmInterestHis.class), eq(InterestHisResDTO.class)))
                    .thenReturn(testInterestHisResDTO);

            // Act
            InterestHisResDTO result = interestHisService.findById(1L);

            // Assert
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("TABLE001", result.getInterestTableId());
            assertEquals("测试利率参数", result.getDescription());
            assertEquals("0", result.getInterestType());
            assertEquals(new BigDecimal("0.05"), result.getAdjustRate());
            assertEquals(new BigDecimal("5.00"), result.getAdjustRatePercent());
            assertEquals(new BigDecimal("0.03"), result.getBaseRate());
            assertEquals(new BigDecimal("3.00"), result.getBaseRatePercent());
            assertEquals("Y", result.getGraceOption());
            assertEquals("N", result.getInterestOnInterestOption());
            assertEquals("360", result.getMonthBase());
            assertEquals("365", result.getYearBase());
            assertEquals("Y", result.getPaymentBackdateOption());
            assertEquals("1", result.getStartDateOption());
            assertEquals("N", result.getWavieOption());
            assertEquals("INT001", result.getInterestBillingTxnCode());
            assertEquals(30, result.getInterestBackDateDays());
            assertEquals("Y", result.getLmtUnitCodeFollowIndicator());
            assertEquals("CREDIT001", result.getCreditIntPostTxnCde());
            assertEquals("1", result.getCreditIntCalType());
            assertEquals("TAX001", result.getIntTaxPostTxnCde());
            assertEquals(new BigDecimal("0.20"), result.getIntTaxRate());
            assertEquals(1, result.getPaymentRestoreBackdateOption());
            assertEquals(15, result.getPaymentRestoreBackdateDays());
            
            verify(pmInterestHisMapper).selectByPrimaryKey(1L);
        }
    }

    /**
     * 测试方法：testFindById_NotFound
     * 用来测试 InterestHisServiceImpl 方法 findById
     * 验证根据ID查询利率历史未找到的场景
     */
    @Test
    void testFindById_NotFound() {
        // Arrange
        when(pmInterestHisMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // Act
        InterestHisResDTO result = interestHisService.findById(1L);

        // Assert
        assertNull(result);
        
        verify(pmInterestHisMapper).selectByPrimaryKey(1L);
    }

    /**
     * 测试方法：testFindById_NullId
     * 用来测试 InterestHisServiceImpl 方法 findById
     * 验证ID为null的场景
     */
    @Test
    void testFindById_NullId() {
        // Arrange
        when(pmInterestHisMapper.selectByPrimaryKey(null)).thenReturn(null);

        // Act
        InterestHisResDTO result = interestHisService.findById(null);

        // Assert
        assertNull(result);
        
        verify(pmInterestHisMapper).selectByPrimaryKey(null);
    }

    /**
     * 测试方法：testFindByOrgAndInterestTableId_NullParameters
     * 用来测试 InterestHisServiceImpl 方法 findByOrgAndInterestTableId
     * 验证参数为null的场景
     */
    @Test
    void testFindByOrgAndInterestTableId_NullParameters() {
        // Arrange
        when(pmInterestHisSelfMapper.selectByOrgAndInterestTableId(null, null))
                .thenReturn(Collections.emptyList());

        // Act
        List<InterestHisResDTO> result = interestHisService.findByOrgAndInterestTableId(null, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(pmInterestHisSelfMapper).selectByOrgAndInterestTableId(null, null);
    }
} 
