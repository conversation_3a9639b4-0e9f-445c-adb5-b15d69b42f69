﻿package com.anytech.anytxn.parameter.authorization.service;

import com.github.pagehelper.Page;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.authorization.service.MccCodeServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccCodeDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccCodeSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmMccCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cglib.beans.BeanCopier;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MccCodeServiceImpl 测试类
 * 测试商户类别码管理服务的所有业务方法
 */
@ExtendWith(MockitoExtension.class)
class MccCodeServiceTest {

    @Mock
    private MccCodeMapper mccCodeMapper;

    @Mock
    private MccCodeSelfMapper mccCodeSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private MccCodeServiceImpl mccCodeService;

    private MccCodeDTO testMccCodeDTO;
    private ParmMccCode testParmMccCode;
    private List<ParmMccCode> testMccCodeList;
    private Page<ParmMccCode> mockPage;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils的静态实例
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        orgNumberUtilField.set(null, mock(OrgNumberUtils.class));

        // 构建测试数据
        buildTestData();
        setupMockPage();
    }

    private void buildTestData() {
        testMccCodeDTO = new MccCodeDTO();
        testMccCodeDTO.setId(123456789L);
        testMccCodeDTO.setMccCde("5411");
        testMccCodeDTO.setDescription("Grocery Stores, Supermarkets");
        testMccCodeDTO.setStatus("Y");
        testMccCodeDTO.setMccLytPtsInd("N");
        testMccCodeDTO.setMccChargeType("01");
        testMccCodeDTO.setMccAbcRollup("A");
        testMccCodeDTO.setMccDcSalesChannel("01");
        testMccCodeDTO.setOrganizationNumber("001");
        testMccCodeDTO.setCreateTime(LocalDateTime.now());
        testMccCodeDTO.setUpdateTime(LocalDateTime.now());
        testMccCodeDTO.setVersionNumber(1L);

        testParmMccCode = new ParmMccCode();
        testParmMccCode.setId(123456789L);
        testParmMccCode.setMccCde("5411");
        testParmMccCode.setDescription("Grocery Stores, Supermarkets");
        testParmMccCode.setStatus("Y");
        testParmMccCode.setMccLytPtsInd("N");
        testParmMccCode.setMccChargeType("01");
        testParmMccCode.setMccAbcRollup("A");
        testParmMccCode.setMccDcSalesChannel("01");
        testParmMccCode.setOrganizationNumber("001");
        testParmMccCode.setCreateTime(LocalDateTime.now());
        testParmMccCode.setUpdateTime(LocalDateTime.now());
        testParmMccCode.setVersionNumber(1L);

        testMccCodeList = Arrays.asList(testParmMccCode);
    }

    private void setupMockPage() {
        mockPage = new Page<>();
        mockPage.setTotal(1L);
        mockPage.setPages(1);
    }

    /**
     * 测试 findListMccCode - 成功分页查询有数据
     */
    @Test
    void testFindListMccCode_Success_WithData() {
        // Arrange
        Integer page = 1;
        Integer rows = 10;
        String mccCde = "5411";
        String description = "Grocery";
        String organizationNumber = "001";

        when(mccCodeSelfMapper.selectByCondition(organizationNumber, mccCde, description))
                .thenReturn(testMccCodeList);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Act
            PageResultDTO<MccCodeDTO> result = mccCodeService.findListMccCode(page, rows, mccCde, description, organizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(page, result.getPage());
            assertEquals(rows, result.getRows());
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            
            MccCodeDTO resultDto = result.getData().get(0);
            assertEquals(testParmMccCode.getMccCde(), resultDto.getMccCde());
            assertEquals(testParmMccCode.getDescription(), resultDto.getDescription());

            verify(mccCodeSelfMapper).selectByCondition(organizationNumber, mccCde, description);
        }
    }

    /**
     * 测试 findListMccCode - 成功分页查询空数据
     */
    @Test
    void testFindListMccCode_Success_EmptyData() {
        // Arrange
        Integer page = 1;
        Integer rows = 10;
        String mccCde = "9999";
        String description = "NonExistent";
        String organizationNumber = "001";

        when(mccCodeSelfMapper.selectByCondition(organizationNumber, mccCde, description))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Act
            PageResultDTO<MccCodeDTO> result = mccCodeService.findListMccCode(page, rows, mccCde, description, organizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(page, result.getPage());
            assertEquals(rows, result.getRows());
            assertNull(result.getData());

            verify(mccCodeSelfMapper).selectByCondition(organizationNumber, mccCde, description);
        }
    }

    /**
     * 测试 findListMccCode - 使用默认机构号
     */
    @Test
    void testFindListMccCode_Success_WithDefaultOrg() {
        // Arrange
        Integer page = 1;
        Integer rows = 10;
        String mccCde = "5411";
        String description = "Grocery";
        String organizationNumber = null;
        String defaultOrg = "001";

        when(mccCodeSelfMapper.selectByCondition(defaultOrg, mccCde, description))
                .thenReturn(testMccCodeList);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(defaultOrg);

            // Act
            PageResultDTO<MccCodeDTO> result = mccCodeService.findListMccCode(page, rows, mccCde, description, organizationNumber);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());

            verify(mccCodeSelfMapper).selectByCondition(defaultOrg, mccCde, description);
            orgNumberUtilsMock.verify(OrgNumberUtils::getOrg, atLeast(1));
        }
    }

    /**
     * 测试 findListMccCode - 数据库异常
     */
    @Test
    void testFindListMccCode_DatabaseException() {
        // Arrange
        Integer page = 1;
        Integer rows = 10;
        String mccCde = "5411";
        String description = "Grocery";
        String organizationNumber = "001";

        when(mccCodeSelfMapper.selectByCondition(organizationNumber, mccCde, description))
                .thenThrow(new RuntimeException("Database error"));

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                    mccCodeService.findListMccCode(page, rows, mccCde, description, organizationNumber));

            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_CODE_FAULT.getCode(), exception.getErrCode());
            verify(mccCodeSelfMapper).selectByCondition(organizationNumber, mccCde, description);
        }
    }

    /**
     * 测试 findMccCode - 成功查询
     */
    @Test
    void testFindMccCode_Success() {
        // Arrange
        Long id = 123456789L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenReturn(testParmMccCode);

        // Act
        MccCodeDTO result = mccCodeService.findMccCode(id);

        // Assert
        assertNotNull(result);
        assertEquals(testParmMccCode.getId(), result.getId());
        assertEquals(testParmMccCode.getMccCde(), result.getMccCde());
        assertEquals(testParmMccCode.getDescription(), result.getDescription());
        assertEquals(testParmMccCode.getStatus(), result.getStatus());

        verify(mccCodeMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 findMccCode - 记录不存在
     */
    @Test
    void testFindMccCode_NotFound() {
        // Arrange
        Long id = 999999999L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                mccCodeService.findMccCode(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(mccCodeMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 findMccCode - 数据库异常
     */
    @Test
    void testFindMccCode_DatabaseException() {
        // Arrange
        Long id = 123456789L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                mccCodeService.findMccCode(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(mccCodeMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 modifyMccCode - 成功修改
     */
    @Test
    void testModifyMccCode_Success() {
        // Arrange
        when(mccCodeMapper.updateByPrimaryKeySelective(any(ParmMccCode.class))).thenReturn(1);

        // Act
        Boolean result = mccCodeService.modifyMccCode(testMccCodeDTO);

        // Assert
        assertTrue(result);
        verify(mccCodeMapper).updateByPrimaryKeySelective(any(ParmMccCode.class));
    }

    /**
     * 测试 modifyMccCode - 修改失败
     */
    @Test
    void testModifyMccCode_Failed() {
        // Arrange
        when(mccCodeMapper.updateByPrimaryKeySelective(any(ParmMccCode.class))).thenReturn(0);

        // Act
        Boolean result = mccCodeService.modifyMccCode(testMccCodeDTO);

        // Assert
        assertFalse(result);
        verify(mccCodeMapper).updateByPrimaryKeySelective(any(ParmMccCode.class));
    }

    /**
     * 测试 modifyMccCode - 数据库异常
     */
    @Test
    void testModifyMccCode_DatabaseException() {
        // Arrange
        when(mccCodeMapper.updateByPrimaryKeySelective(any(ParmMccCode.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                mccCodeService.modifyMccCode(testMccCodeDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_CODE_FAULT.getCode(), exception.getErrCode());
        verify(mccCodeMapper).updateByPrimaryKeySelective(any(ParmMccCode.class));
    }

    /**
     * 测试 removeMccCode - 成功删除
     */
    @Test
    void testRemoveMccCode_Success() {
        // Arrange
        Long id = 123456789L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenReturn(testParmMccCode);
        when(mccCodeMapper.deleteByPrimaryKey(id)).thenReturn(1);

        // Act
        Boolean result = mccCodeService.removeMccCode(id);

        // Assert
        assertTrue(result);
        verify(mccCodeMapper).selectByPrimaryKey(id);
        verify(mccCodeMapper).deleteByPrimaryKey(id);
    }

    /**
     * 测试 removeMccCode - 记录不存在
     */
    @Test
    void testRemoveMccCode_NotFound() {
        // Arrange
        Long id = 999999999L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                mccCodeService.removeMccCode(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(mccCodeMapper).selectByPrimaryKey(id);
        verify(mccCodeMapper, never()).deleteByPrimaryKey(anyLong());
    }

    /**
     * 测试 removeMccCode - 删除失败
     */
    @Test
    void testRemoveMccCode_DeleteFailed() {
        // Arrange
        Long id = 123456789L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenReturn(testParmMccCode);
        when(mccCodeMapper.deleteByPrimaryKey(id)).thenReturn(0);

        // Act
        Boolean result = mccCodeService.removeMccCode(id);

        // Assert
        assertFalse(result);
        verify(mccCodeMapper).selectByPrimaryKey(id);
        verify(mccCodeMapper).deleteByPrimaryKey(id);
    }

    /**
     * 测试 removeMccCode - 数据库异常
     */
    @Test
    void testRemoveMccCode_DatabaseException() {
        // Arrange
        Long id = 123456789L;
        when(mccCodeMapper.selectByPrimaryKey(id)).thenReturn(testParmMccCode);
        when(mccCodeMapper.deleteByPrimaryKey(id)).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                mccCodeService.removeMccCode(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_CODE_FAULT.getCode(), exception.getErrCode());
        verify(mccCodeMapper).selectByPrimaryKey(id);
        verify(mccCodeMapper).deleteByPrimaryKey(id);
    }

    /**
     * 测试 addMccCode - 成功添加
     */
    @Test
    void testAddMccCode_Success() {
        // Arrange
        Long generatedId = 987654321L;
        String tenantId = "tenant001";

        when(mccCodeSelfMapper.isExists(testMccCodeDTO.getMccCde(), testMccCodeDTO.getOrganizationNumber()))
                .thenReturn(0);
        when(numberIdGenerator.generateId(tenantId)).thenReturn(generatedId);
        when(mccCodeMapper.insertSelective(any(ParmMccCode.class))).thenReturn(1);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(any(MccCodeDTO.class), eq(ParmMccCode.class)))
                    .thenReturn(testParmMccCode);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(tenantId);

            // Act
            Boolean result = mccCodeService.addMccCode(testMccCodeDTO);

            // Assert
            assertTrue(result);
            verify(mccCodeSelfMapper).isExists(testMccCodeDTO.getMccCde(), testMccCodeDTO.getOrganizationNumber());
            verify(numberIdGenerator).generateId(tenantId);
            verify(mccCodeMapper).insertSelective(any(ParmMccCode.class));
            
            beanMappingMock.verify(() -> BeanMapping.copy(any(MccCodeDTO.class), eq(ParmMccCode.class)));
            tenantUtilsMock.verify(TenantUtils::getTenantId);
        }
    }

    /**
     * 测试 addMccCode - 参数为空
     */
    @Test
    void testAddMccCode_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                mccCodeService.addMccCode(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 addMccCode - 记录已存在
     */
    @Test
    void testAddMccCode_AlreadyExists() {
        // Arrange
        when(mccCodeSelfMapper.isExists(testMccCodeDTO.getMccCde(), testMccCodeDTO.getOrganizationNumber()))
                .thenReturn(1);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(MccCodeDTO.class), eq(ParmMccCode.class)))
                    .thenReturn(testParmMccCode);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                    mccCodeService.addMccCode(testMccCodeDTO));

            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_CODE_FAULT.getCode(), exception.getErrCode());
            verify(mccCodeSelfMapper).isExists(testMccCodeDTO.getMccCde(), testMccCodeDTO.getOrganizationNumber());
            
            beanMappingMock.verify(() -> BeanMapping.copy(any(MccCodeDTO.class), eq(ParmMccCode.class)));
        }
    }

    /**
     * 测试 addMccCode - 数据库插入异常
     */
    @Test
    void testAddMccCode_DatabaseException() {
        // Arrange
        Long generatedId = 987654321L;
        String tenantId = "tenant001";

        when(mccCodeSelfMapper.isExists(testMccCodeDTO.getMccCde(), testMccCodeDTO.getOrganizationNumber()))
                .thenReturn(0);
        when(numberIdGenerator.generateId(tenantId)).thenReturn(generatedId);
        when(mccCodeMapper.insertSelective(any(ParmMccCode.class)))
                .thenThrow(new RuntimeException("Database error"));

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(any(MccCodeDTO.class), eq(ParmMccCode.class)))
                    .thenReturn(testParmMccCode);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn(tenantId);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                    mccCodeService.addMccCode(testMccCodeDTO));

            assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_CODE_FAULT.getCode(), exception.getErrCode());
            verify(mccCodeSelfMapper).isExists(testMccCodeDTO.getMccCde(), testMccCodeDTO.getOrganizationNumber());
            verify(numberIdGenerator).generateId(tenantId);
            verify(mccCodeMapper).insertSelective(any(ParmMccCode.class));
            
            beanMappingMock.verify(() -> BeanMapping.copy(any(MccCodeDTO.class), eq(ParmMccCode.class)));
            tenantUtilsMock.verify(TenantUtils::getTenantId);
        }
    }

    /**
     * 测试 getMccCodes - 成功获取所有商户类别码
     */
    @Test
    void testGetMccCodes_Success() {
        // Arrange
        String organizationNumber = "001";
        when(mccCodeSelfMapper.selectAll(true, organizationNumber)).thenReturn(testMccCodeList);

        // Act
        List<Map<String, String>> result = mccCodeService.getMccCodes(organizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Map<String, String> resultMap = result.get(0);
        assertEquals(testParmMccCode.getMccCde(), resultMap.get("value"));
        assertEquals(testParmMccCode.getDescription(), resultMap.get("label"));

        verify(mccCodeSelfMapper).selectAll(true, organizationNumber);
    }

    /**
     * 测试 getMccCodes - 空数据
     */
    @Test
    void testGetMccCodes_EmptyData() {
        // Arrange
        String organizationNumber = "001";
        when(mccCodeSelfMapper.selectAll(true, organizationNumber)).thenReturn(Collections.emptyList());

        // Act
        List<Map<String, String>> result = mccCodeService.getMccCodes(organizationNumber);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(mccCodeSelfMapper).selectAll(true, organizationNumber);
    }

    /**
     * 测试 selectMccByCondition - 成功查询有数据
     */
    @Test
    void testSelectMccByCondition_Success_WithData() {
        // Arrange
        String organizationNumber = "001";
        String mccCde = "5411";
        String status = "Y";

        when(mccCodeSelfMapper.selectMccByCondition(organizationNumber, mccCde, status))
                .thenReturn(testMccCodeList);

        // Act
        List<MccCodeDTO> result = mccCodeService.selectMccByCondition(organizationNumber, mccCde, status);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        MccCodeDTO resultDto = result.get(0);
        assertEquals(testParmMccCode.getMccCde(), resultDto.getMccCde());
        assertEquals(testParmMccCode.getDescription(), resultDto.getDescription());
        assertEquals(testParmMccCode.getStatus(), resultDto.getStatus());

        verify(mccCodeSelfMapper).selectMccByCondition(organizationNumber, mccCde, status);
    }

    /**
     * 测试 selectMccByCondition - 查询结果为空
     */
    @Test
    void testSelectMccByCondition_Success_EmptyData() {
        // Arrange
        String organizationNumber = "001";
        String mccCde = "9999";
        String status = "N";

        when(mccCodeSelfMapper.selectMccByCondition(organizationNumber, mccCde, status))
                .thenReturn(Collections.emptyList());

        // Act
        List<MccCodeDTO> result = mccCodeService.selectMccByCondition(organizationNumber, mccCde, status);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(mccCodeSelfMapper).selectMccByCondition(organizationNumber, mccCde, status);
    }
}
