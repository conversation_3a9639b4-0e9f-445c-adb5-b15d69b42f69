﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CountryMccCheckDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.service.CountryMccCheckServiceImpl;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccCheckMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccCheckSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmCountryMccCheck;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CountryMccCheckService单元测试类
 * 
 * 测试9个核心方法：
 * 1. findListCountryMccCheck - 分页查询
 * 2. findCountryMccCheck - 根据ID查询
 * 3. modifyCountryMccCheck - 修改
 * 4. removeCountryMccCheck - 删除
 * 5. addCountryMccCheck - 新增
 * 6. getListByMccCode - 根据MCC码获取列表
 * 7. getListByCountryCode - 根据国家码获取列表
 * 8. modifyCountryMccCheckByCode - 根据代码修改
 * 9. deleteByCountryCode - 根据国家码删除
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CountryMccCheckServiceTest {

    @Mock
    private ParmCountryMccCheckMapper parmCountryMccCheckMapper;

    @Mock
    private ParmCountryMccCheckSelfMapper parmCountryMccCheckSelfMapper;

    @InjectMocks
    private CountryMccCheckServiceImpl countryMccCheckService;

    private static MockedStatic<OrgNumberUtils> mockedOrgNumberUtils;

    private CountryMccCheckDTO countryMccCheckDTO;
    private ParmCountryMccCheck parmCountryMccCheck;

    @BeforeAll
    static void setUpClass() {
        // 预先处理OrgNumberUtils静态初始化问题
        mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class);
        mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("1001");
    }

    @AfterAll
    static void tearDownClass() {
        // 清理MockedStatic资源
        if (mockedOrgNumberUtils != null) {
            mockedOrgNumberUtils.close();
        }
    }

    @BeforeEach
    void setUp() {
        // 创建DTO测试数据
        countryMccCheckDTO = new CountryMccCheckDTO();
        countryMccCheckDTO.setId(new BigDecimal("1"));
        countryMccCheckDTO.setNumericCountryCode("156");
        countryMccCheckDTO.setMccCde("5411");
        countryMccCheckDTO.setCountryDescription("中国");
        countryMccCheckDTO.setMccDescription("杂货店");

        // 创建Model测试数据
        parmCountryMccCheck = new ParmCountryMccCheck();
        parmCountryMccCheck.setId(new BigDecimal("1"));
        parmCountryMccCheck.setNumericCountryCode("156");
        parmCountryMccCheck.setMccCde("5411");
        parmCountryMccCheck.setCountryDescription("中国");
        parmCountryMccCheck.setMccDescription("杂货店");
    }

    // ==================== findCountryMccCheck 测试 ====================

    @Test
    void testFindCountryMccCheck_Success() {
        // Arrange
        when(parmCountryMccCheckMapper.selectByPrimaryKey(new BigDecimal("1"))).thenReturn(parmCountryMccCheck);

        // Act
        CountryMccCheckDTO result = countryMccCheckService.findCountryMccCheck(1L);

        // Assert
        assertNotNull(result);
        verify(parmCountryMccCheckMapper).selectByPrimaryKey(new BigDecimal("1"));
    }

    @Test
    void testFindCountryMccCheck_NotFound() {
        // Arrange
        when(parmCountryMccCheckMapper.selectByPrimaryKey(new BigDecimal("1"))).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.findCountryMccCheck(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFindCountryMccCheck_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckMapper.selectByPrimaryKey(new BigDecimal("1")))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.findCountryMccCheck(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== modifyCountryMccCheck 测试 ====================

    @Test
    void testModifyCountryMccCheck_Success() {
        // Arrange
        when(parmCountryMccCheckMapper.updateByPrimaryKeySelective(any(ParmCountryMccCheck.class))).thenReturn(1);

        // Act
        Boolean result = countryMccCheckService.modifyCountryMccCheck(countryMccCheckDTO);

        // Assert
        assertTrue(result);
        verify(parmCountryMccCheckMapper).updateByPrimaryKeySelective(any(ParmCountryMccCheck.class));
    }

    @Test
    void testModifyCountryMccCheck_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckMapper.updateByPrimaryKeySelective(any(ParmCountryMccCheck.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.modifyCountryMccCheck(countryMccCheckDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DETAIL_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== removeCountryMccCheck 测试 ====================

    @Test
    void testRemoveCountryMccCheck_Success() {
        // Arrange
        when(parmCountryMccCheckMapper.selectByPrimaryKey(new BigDecimal("1"))).thenReturn(parmCountryMccCheck);
        when(parmCountryMccCheckMapper.deleteByPrimaryKey(new BigDecimal("1"))).thenReturn(1);

        // Act
        Boolean result = countryMccCheckService.removeCountryMccCheck(1L);

        // Assert
        assertTrue(result);
        verify(parmCountryMccCheckMapper).deleteByPrimaryKey(new BigDecimal("1"));
    }

    @Test
    void testRemoveCountryMccCheck_NotExists() {
        // Arrange
        when(parmCountryMccCheckMapper.selectByPrimaryKey(new BigDecimal("1"))).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.removeCountryMccCheck(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveCountryMccCheck_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckMapper.selectByPrimaryKey(new BigDecimal("1"))).thenReturn(parmCountryMccCheck);
        when(parmCountryMccCheckMapper.deleteByPrimaryKey(new BigDecimal("1")))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.removeCountryMccCheck(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DETAIL_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== addCountryMccCheck 测试 ====================

    @Test
    void testAddCountryMccCheck_Success() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.isExists("156", "5411")).thenReturn(0);
        when(parmCountryMccCheckMapper.insertSelective(any(ParmCountryMccCheck.class))).thenReturn(1);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CountryMccCheckDTO.class), eq(ParmCountryMccCheck.class)))
                    .thenReturn(parmCountryMccCheck);

            // Act
            Boolean result = countryMccCheckService.addCountryMccCheck(countryMccCheckDTO);

            // Assert
            assertTrue(result);
            verify(parmCountryMccCheckMapper).insertSelective(any(ParmCountryMccCheck.class));
        }
    }

    @Test
    void testAddCountryMccCheck_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.addCountryMccCheck(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAddCountryMccCheck_AlreadyExists() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.isExists("156", "5411")).thenReturn(1);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CountryMccCheckDTO.class), eq(ParmCountryMccCheck.class)))
                    .thenReturn(parmCountryMccCheck);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> countryMccCheckService.addCountryMccCheck(countryMccCheckDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_GROUP_DETAIL_FAULT.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testAddCountryMccCheck_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.isExists("156", "5411")).thenReturn(0);
        when(parmCountryMccCheckMapper.insertSelective(any(ParmCountryMccCheck.class)))
                .thenThrow(new RuntimeException("Database error"));

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CountryMccCheckDTO.class), eq(ParmCountryMccCheck.class)))
                    .thenReturn(parmCountryMccCheck);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> countryMccCheckService.addCountryMccCheck(countryMccCheckDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_GROUP_DETAIL_FAULT.getCode(), exception.getErrCode());
        }
    }

    // ==================== getListByMccCode 测试 ====================

    @Test
    void testGetListByMccCode_Success() {
        // Arrange
        List<ParmCountryMccCheck> parmCountryMccCheckList = Arrays.asList(parmCountryMccCheck);
        when(parmCountryMccCheckSelfMapper.selectListByMccCde("5411")).thenReturn(parmCountryMccCheckList);

        // Act
        List<CountryMccCheckDTO> result = countryMccCheckService.getListByMccCode("5411");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(parmCountryMccCheckSelfMapper).selectListByMccCde("5411");
    }

    @Test
    void testGetListByMccCode_EmptyResult() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.selectListByMccCde("5411")).thenReturn(new ArrayList<>());

        // Act
        List<CountryMccCheckDTO> result = countryMccCheckService.getListByMccCode("5411");

        // Assert
        assertNull(result);
    }

    @Test
    void testGetListByMccCode_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.selectListByMccCde("5411"))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.getListByMccCode("5411"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_MCC_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== getListByCountryCode 测试 ====================

    @Test
    void testGetListByCountryCode_Success() {
        // Arrange
        List<ParmCountryMccCheck> parmCountryMccCheckList = Arrays.asList(parmCountryMccCheck);
        when(parmCountryMccCheckSelfMapper.selectListByCountryCode("156")).thenReturn(parmCountryMccCheckList);

        // Act
        List<CountryMccCheckDTO> result = countryMccCheckService.getListByCountryCode("156");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(parmCountryMccCheckSelfMapper).selectListByCountryCode("156");
    }

    @Test
    void testGetListByCountryCode_EmptyResult() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.selectListByCountryCode("156")).thenReturn(new ArrayList<>());

        // Act
        List<CountryMccCheckDTO> result = countryMccCheckService.getListByCountryCode("156");

        // Assert
        assertNull(result);
    }

    @Test
    void testGetListByCountryCode_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.selectListByCountryCode("156"))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.getListByCountryCode("156"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DETAIL_BY_MCC_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== modifyCountryMccCheckByCode 测试 ====================

    @Test
    void testModifyCountryMccCheckByCode_Success() {
        // Arrange
        when(parmCountryMccCheckMapper.updateByPrimaryKeySelective(any(ParmCountryMccCheck.class))).thenReturn(1);

        // Act
        Integer result = countryMccCheckService.modifyCountryMccCheckByCode(countryMccCheckDTO);

        // Assert
        assertEquals(Integer.valueOf(1), result);
        verify(parmCountryMccCheckMapper).updateByPrimaryKeySelective(any(ParmCountryMccCheck.class));
    }

    @Test
    void testModifyCountryMccCheckByCode_DatabaseException() {
        // Arrange
        when(parmCountryMccCheckMapper.updateByPrimaryKeySelective(any(ParmCountryMccCheck.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.modifyCountryMccCheckByCode(countryMccCheckDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DETAIL_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== deleteByCountryCode 测试 ====================

    @Test
    void testDeleteByCountryCode_Success() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.deleteByCountryCode("156")).thenReturn(1);

        // Act
        boolean result = countryMccCheckService.deleteByCountryCode("156");

        // Assert
        assertTrue(result);
        verify(parmCountryMccCheckSelfMapper).deleteByCountryCode("156");
    }

    @Test
    void testDeleteByCountryCode_NoRecordsDeleted() {
        // Arrange
        when(parmCountryMccCheckSelfMapper.deleteByCountryCode("156")).thenReturn(0);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccCheckService.deleteByCountryCode("156"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DETAIL_FAULT.getCode(), exception.getErrCode());
    }
} 
