﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CountryMccDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICountryMccService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.service.CountryMccServiceImpl;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmCountryMccSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCountryCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCountryCode;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmCountryMcc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CountryMccService简化版单元测试类
 * 
 * 专注于6个核心方法：
 * 1. findListCountryMcc - 分页查询
 * 2. findCountryMcc - 根据ID查询
 * 3. modifyCountryMcc - 修改
 * 4. removeCountryMcc - 删除
 * 5. addCountryMcc - 新增
 * 6. getMccByCountry - 根据国家码获取MCC列表
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class CountryMccServiceSimpleTest {

    @Mock
    private ParmCountryMccMapper parmCountryMccMapper;

    @Mock
    private ParmCountryMccSelfMapper parmCountryMccSelfMapper;

    @Mock
    private ParmCountryCodeSelfMapper parmCountryCodeSelfMapper;

    @InjectMocks
    private CountryMccServiceImpl countryMccService;

    private static MockedStatic<OrgNumberUtils> mockedOrgNumberUtils;

    private CountryMccDTO countryMccDTO;
    private ParmCountryMcc parmCountryMcc;
    private ParmCountryCode parmCountryCode;

    @BeforeAll
    static void setUpClass() {
        // 预先处理OrgNumberUtils静态初始化问题
        mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class);
        mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("1001");
    }

    @AfterAll
    static void tearDownClass() {
        // 清理MockedStatic资源
        if (mockedOrgNumberUtils != null) {
            mockedOrgNumberUtils.close();
        }
    }

    @BeforeEach
    void setUp() {
        // 创建DTO测试数据
        countryMccDTO = new CountryMccDTO();
        countryMccDTO.setId(1L);
        countryMccDTO.setCountryName("中国");
        countryMccDTO.setIsoGeoCodeNumeric(156);  // DTO中是Integer
        countryMccDTO.setMcc("5411");
        countryMccDTO.setDescription("杂货店");
        countryMccDTO.setTriggerAmount(new BigDecimal("1000.00"));
        countryMccDTO.setAllMcc("N");

        // 创建Model测试数据
        parmCountryMcc = new ParmCountryMcc();
        parmCountryMcc.setId(1L);
        parmCountryMcc.setCountryName("中国");
        parmCountryMcc.setIsoGeoCodeNumeric("156");  // Model中是String
        parmCountryMcc.setMcc("5411");
        parmCountryMcc.setDescription("杂货店");
        parmCountryMcc.setTriggerAmount(new BigDecimal("1000.00"));
        parmCountryMcc.setAllMcc("N");

        // 创建ParmCountryCode测试数据
        parmCountryCode = new ParmCountryCode();
        parmCountryCode.setId("1");
        parmCountryCode.setNumericCountryCode("156");
        parmCountryCode.setDescription("中国");
    }

    // ==================== findListCountryMcc 测试 ====================

    @Test
    void testFindListCountryMcc_Success() {
        // Arrange
        List<ParmCountryMcc> parmCountryMccList = Arrays.asList(parmCountryMcc);

        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class)) {
            Page<ParmCountryMcc> pageInfo = new Page<>(1, 10);
            pageInfo.setTotal(1L);
            pageInfo.setPages(1);
            
            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(pageInfo);
            when(parmCountryMccSelfMapper.selectAllByOrganizationNumber("1001")).thenReturn(parmCountryMccList);

            // Act
            PageResultDTO<CountryMccDTO> result = countryMccService.findListCountryMcc(1, 10, null);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1L, result.getTotalPage());
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            assertEquals("中国", result.getData().get(0).getCountryName());
        }
    }

    @Test
    void testFindListCountryMcc_DatabaseException() {
        // Arrange
        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class)) {
            Page<ParmCountryMcc> pageInfo = new Page<>(1, 10);
            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(pageInfo);
            when(parmCountryMccSelfMapper.selectAllByOrganizationNumber("1001"))
                    .thenThrow(new RuntimeException("Database error"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> countryMccService.findListCountryMcc(1, 10, null));
            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_CODE_FAULT.getCode(), exception.getErrCode());
        }
    }

    // ==================== findCountryMcc 测试 ====================

    @Test
    void testFindCountryMcc_Success() {
        // Arrange
        when(parmCountryMccMapper.selectByPrimaryKey(1L)).thenReturn(parmCountryMcc);

        // Act
        CountryMccDTO result = countryMccService.findCountryMcc(1L);

        // Assert
        assertNotNull(result);
        assertEquals("中国", result.getCountryName());
        assertEquals("5411", result.getMcc());
    }

    @Test
    void testFindCountryMcc_NotFound() {
        // Arrange
        when(parmCountryMccMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccService.findCountryMcc(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFindCountryMcc_DatabaseException() {
        // Arrange
        when(parmCountryMccMapper.selectByPrimaryKey(1L))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccService.findCountryMcc(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== modifyCountryMcc 测试 ====================

    @Test
    void testModifyCountryMcc_Success() {
        // Arrange
        when(parmCountryMccMapper.updateByPrimaryKeySelective(any(ParmCountryMcc.class))).thenReturn(1);

        // Act
        Boolean result = countryMccService.modifyCountryMcc(countryMccDTO);

        // Assert
        assertTrue(result);
    }

    @Test
    void testModifyCountryMcc_DatabaseException() {
        // Arrange
        when(parmCountryMccMapper.updateByPrimaryKeySelective(any(ParmCountryMcc.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccService.modifyCountryMcc(countryMccDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_CODE_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== removeCountryMcc 测试 ====================

    @Test
    void testRemoveCountryMcc_Success() {
        // Arrange
        when(parmCountryMccMapper.selectByPrimaryKey(1L)).thenReturn(parmCountryMcc);
        when(parmCountryMccMapper.deleteByPrimaryKey(1L)).thenReturn(1);

        // Act
        Boolean result = countryMccService.removeCountryMcc(1L);

        // Assert
        assertTrue(result);
    }

    @Test
    void testRemoveCountryMcc_NotExists() {
        // Arrange
        when(parmCountryMccMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccService.removeCountryMcc(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_CODE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveCountryMcc_DatabaseException() {
        // Arrange
        when(parmCountryMccMapper.selectByPrimaryKey(1L)).thenReturn(parmCountryMcc);
        when(parmCountryMccMapper.deleteByPrimaryKey(1L))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccService.removeCountryMcc(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_CODE_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== addCountryMcc 测试 ====================

    @Test
    void testAddCountryMcc_Success() {
        // Arrange
        when(parmCountryCodeSelfMapper.selectByDescription("中国")).thenReturn(parmCountryCode);
        when(parmCountryMccSelfMapper.existsCountryMcc("156", "5411")).thenReturn(0);
        when(parmCountryMccMapper.insert(any(ParmCountryMcc.class))).thenReturn(1);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CountryMccDTO.class), eq(ParmCountryMcc.class)))
                    .thenReturn(parmCountryMcc);

            // Act
            Boolean result = countryMccService.addCountryMcc(countryMccDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    void testAddCountryMcc_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> countryMccService.addCountryMcc(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAddCountryMcc_AlreadyExists() {
        // Arrange
        when(parmCountryCodeSelfMapper.selectByDescription("中国")).thenReturn(parmCountryCode);
        when(parmCountryMccSelfMapper.existsCountryMcc("156", "5411")).thenReturn(1);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CountryMccDTO.class), eq(ParmCountryMcc.class)))
                    .thenReturn(parmCountryMcc);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> countryMccService.addCountryMcc(countryMccDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_CODE_FAULT.getCode(), exception.getErrCode());
        }
    }

    // ==================== getMccByCountry 测试 ====================

    @Test
    void testGetMccByCountry_Success() {
        // Arrange
        List<ParmCountryMcc> parmCountryMccList = Arrays.asList(parmCountryMcc);
        when(parmCountryMccSelfMapper.selectAll("156")).thenReturn(parmCountryMccList);

        // Act
        List<Map<String, String>> result = countryMccService.getMccByCountry("156");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("5411", result.get(0).get("value"));
        assertEquals("杂货店", result.get(0).get("label"));
    }

    @Test
    void testGetMccByCountry_EmptyResult() {
        // Arrange
        when(parmCountryMccSelfMapper.selectAll("156")).thenReturn(new ArrayList<>());

        // Act
        List<Map<String, String>> result = countryMccService.getMccByCountry("156");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // ==================== existsCountryMcc 测试 ====================

    @Test
    void testExistsCountryMcc_Exists() {
        // Arrange
        when(parmCountryMccSelfMapper.existsCountryMcc("156", "5411")).thenReturn(1);

        // Act
        Boolean result = countryMccService.existsCountryMcc("156", "5411");

        // Assert
        assertTrue(result);
    }

    @Test
    void testExistsCountryMcc_NotExists() {
        // Arrange
        when(parmCountryMccSelfMapper.existsCountryMcc("156", "5411")).thenReturn(0);

        // Act
        Boolean result = countryMccService.existsCountryMcc("156", "5411");

        // Assert
        assertFalse(result);
    }
} 
