﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.AuthorizationRuleMapper;
import com.anytech.anytxn.parameter.authorization.mapper.AuthorizationRuleSelfMapper;
import com.anytech.anytxn.parameter.authorization.service.AuthorizationRuleServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleSearchDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.AuthorizationRule;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthorizationRuleService单元测试类
 * 
 * 测试场景：
 * - 分页查询授权检查参数列表
 * - 根据主键查询授权检查参数
 * - 修改授权检查参数
 * - 删除授权检查参数
 * - 新增授权检查参数
 * - 根据tableId和机构号查询
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
class AuthorizationRuleServiceTest {

    @Mock
    private AuthorizationRuleMapper authorizationRuleMapper;

    @Mock
    private AuthorizationRuleSelfMapper authorizationRuleSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private AuthorizationRuleServiceImpl authorizationRuleService;

    private String testOrganizationNumber;
    private String testTableId;
    private String testId;
    private AuthorizationRule testAuthorizationRule;
    private AuthorizationRuleDTO testAuthorizationRuleDTO;
    private AuthorizationRuleSearchDTO testSearchDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 测试基础数据
        testOrganizationNumber = "001";
        testTableId = "TBL001";
        testId = "123456789";

        // 通过反射初始化OrgNumberUtils
        try {
            Class<?> orgNumberUtilsClass = OrgNumberUtils.class;
            java.lang.reflect.Field orgNumberUtilField = orgNumberUtilsClass.getDeclaredField("orgNumberUtil");
            orgNumberUtilField.setAccessible(true);
            
            // 创建Mock实例
            OrgNumberUtils mockOrgNumberUtils = mock(OrgNumberUtils.class);
            when(mockOrgNumberUtils.getBatchOrg()).thenReturn(testOrganizationNumber);
            
            // 设置静态字段
            orgNumberUtilField.set(null, mockOrgNumberUtils);
        } catch (Exception e) {
            // 忽略反射异常
        }

        // 初始化AuthorizationRule
        testAuthorizationRule = new AuthorizationRule();
        testAuthorizationRule.setId(testId);
        testAuthorizationRule.setTableId(testTableId);
        testAuthorizationRule.setOrganizationNumber(testOrganizationNumber);
        testAuthorizationRule.setMaxCleAprCnt(10);
        testAuthorizationRule.setMaxCleDceCnt(5);
        testAuthorizationRule.setMaxDlyAprCnt(20);
        testAuthorizationRule.setMaxDlyDceCnt(8);
        testAuthorizationRule.setDescription("测试授权检查参数");
        testAuthorizationRule.setStatus("A");
        testAuthorizationRule.setFstUsgDaysNbr(30);
        testAuthorizationRule.setFstUsgChkAmt(new BigDecimal("1000"));
        testAuthorizationRule.setMaxPinErrCnt(3);
        testAuthorizationRule.setMaxCvvErrCnt(3);
        testAuthorizationRule.setMaxCvv2ErrCnt(3);
        testAuthorizationRule.setRemovalLimitDays(7);

        // 初始化AuthorizationRuleDTO
        testAuthorizationRuleDTO = new AuthorizationRuleDTO();
        testAuthorizationRuleDTO.setId(testId);
        testAuthorizationRuleDTO.setTableId(testTableId);
        testAuthorizationRuleDTO.setOrganizationNumber(testOrganizationNumber);
        testAuthorizationRuleDTO.setMaxCleAprCnt(10);
        testAuthorizationRuleDTO.setMaxCleDceCnt(5);
        testAuthorizationRuleDTO.setMaxDlyAprCnt(20);
        testAuthorizationRuleDTO.setMaxDlyDceCnt(8);
        testAuthorizationRuleDTO.setDescription("测试授权检查参数");
        testAuthorizationRuleDTO.setStatus("A");
        testAuthorizationRuleDTO.setFstUsgDaysNbr(30);
        testAuthorizationRuleDTO.setFstUsgChkAmt(new BigDecimal("1000"));
        testAuthorizationRuleDTO.setMaxPinErrCnt(3);
        testAuthorizationRuleDTO.setMaxCvvErrCnt(3);
        testAuthorizationRuleDTO.setMaxCvv2ErrCnt(3);
        testAuthorizationRuleDTO.setRemovalLimitDays(7);

        // 初始化搜索DTO
        testSearchDTO = new AuthorizationRuleSearchDTO();
        testSearchDTO.setOrganizationNumber(testOrganizationNumber);
        testSearchDTO.setTableId(testTableId);
        testSearchDTO.setDescription("测试");
    }

    /**
     * 测试分页查询授权检查参数列表 - 成功场景
     */
    @Test
    void testFindListAuthorizationRule_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(authorizationRuleSelfMapper.selectByCondition(any(AuthorizationRuleSearchDTO.class)))
                .thenReturn(Arrays.asList(testAuthorizationRule));

            // Act
            PageResultDTO<AuthorizationRuleDTO> result = authorizationRuleService.findListAuthorizationRule(1, 10, testSearchDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals(testTableId, result.getData().get(0).getTableId());
            verify(authorizationRuleSelfMapper).selectByCondition(any(AuthorizationRuleSearchDTO.class));
        }
    }

    /**
     * 测试分页查询授权检查参数列表 - 空搜索条件
     */
    @Test
    void testFindListAuthorizationRule_NullSearchDTO() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(authorizationRuleSelfMapper.selectByCondition(any(AuthorizationRuleSearchDTO.class)))
                .thenReturn(Collections.emptyList());

            // Act
            PageResultDTO<AuthorizationRuleDTO> result = authorizationRuleService.findListAuthorizationRule(1, 10, null);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getTotal());
            assertTrue(result.getData() == null || result.getData().size() == 0);
        }
    }

    /**
     * 测试分页查询授权检查参数列表 - 数字格式异常
     */
    @Test
    void testFindListAuthorizationRule_NumberFormatException() {
        // Arrange
        testSearchDTO.setMaxCleAprCnt("invalid_number");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.findListAuthorizationRule(1, 10, testSearchDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION.getCode(), exception.getErrCode());
    }

    /**
     * 测试分页查询授权检查参数列表 - 数据库异常
     */
    @Test
    void testFindListAuthorizationRule_DatabaseException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(authorizationRuleSelfMapper.selectByCondition(any(AuthorizationRuleSearchDTO.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                authorizationRuleService.findListAuthorizationRule(1, 10, testSearchDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_AUTH_RULE_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试根据主键查询授权检查参数 - 成功场景
     */
    @Test
    void testFindAuthorizationRule_Success() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenReturn(testAuthorizationRule);

        // Act
        AuthorizationRuleDTO result = authorizationRuleService.findAuthorizationRule(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testId, result.getId());
        assertEquals(testTableId, result.getTableId());
        assertEquals(testOrganizationNumber, result.getOrganizationNumber());
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据主键查询授权检查参数 - 记录不存在
     */
    @Test
    void testFindAuthorizationRule_RecordNotFound() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.findAuthorizationRule(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据主键查询授权检查参数 - 数据库异常
     */
    @Test
    void testFindAuthorizationRule_DatabaseException() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.findAuthorizationRule(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试修改授权检查参数 - 成功场景
     */
    @Test
    void testModifyAuthorizationRule_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            AuthorizationRule newAuthorizationRule = new AuthorizationRule();
            newAuthorizationRule.setId(testId);
            newAuthorizationRule.setTableId(testTableId);
            
            beanMappingMock.when(() -> BeanMapping.copy(any(AuthorizationRuleDTO.class), eq(AuthorizationRule.class)))
                .thenReturn(newAuthorizationRule);
            
            when(authorizationRuleMapper.selectByPrimaryKey(testId))
                .thenReturn(testAuthorizationRule);

            // Act
            ParameterCompare result = authorizationRuleService.modifyAuthorizationRule(testAuthorizationRuleDTO);

            // Assert
            assertNotNull(result);
            verify(authorizationRuleMapper).selectByPrimaryKey(testId);
        }
    }

    /**
     * 测试修改授权检查参数 - 记录不存在
     */
    @Test
    void testModifyAuthorizationRule_RecordNotFound() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.modifyAuthorizationRule(testAuthorizationRuleDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(), exception.getErrCode());
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试删除授权检查参数 - 成功场景
     */
    @Test
    void testRemoveAuthorizationRule_Success() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenReturn(testAuthorizationRule);

        // Act
        ParameterCompare result = authorizationRuleService.removeAuthorizationRule(testId);

        // Assert
        assertNotNull(result);
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试删除授权检查参数 - 记录不存在
     */
    @Test
    void testRemoveAuthorizationRule_RecordNotFound() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.removeAuthorizationRule(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据机构号和参数表ID删除 - 成功场景
     */
    @Test
    void testRemoveByOrgAndTableId_Success() {
        // Arrange
        when(authorizationRuleSelfMapper.selectByTableId(testTableId, testOrganizationNumber))
            .thenReturn(testAuthorizationRule);

        // Act
        ParameterCompare result = authorizationRuleService.removeByOrgAndTableId(testOrganizationNumber, testTableId);

        // Assert
        assertNotNull(result);
        verify(authorizationRuleSelfMapper).selectByTableId(testTableId, testOrganizationNumber);
    }

    /**
     * 测试根据机构号和参数表ID删除 - 记录不存在
     */
    @Test
    void testRemoveByOrgAndTableId_RecordNotFound() {
        // Arrange
        when(authorizationRuleSelfMapper.selectByTableId(testTableId, testOrganizationNumber))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.removeByOrgAndTableId(testOrganizationNumber, testTableId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_RULE_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(authorizationRuleSelfMapper).selectByTableId(testTableId, testOrganizationNumber);
    }

    /**
     * 测试新增授权检查参数 - 成功场景
     */
    @Test
    void testAddAuthorizationRule_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            AuthorizationRule newAuthorizationRule = new AuthorizationRule();
            newAuthorizationRule.setTableId(testTableId);
            
            beanMappingMock.when(() -> BeanMapping.copy(any(AuthorizationRuleDTO.class), eq(AuthorizationRule.class)))
                .thenReturn(newAuthorizationRule);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(authorizationRuleSelfMapper.isExists(testTableId, testOrganizationNumber))
                .thenReturn(0);
            when(numberIdGenerator.generateId("tenant001"))
                .thenReturn(123456789L);

            // Act
            ParameterCompare result = authorizationRuleService.addAuthorizationRule(testAuthorizationRuleDTO);

            // Assert
            assertNotNull(result);
            verify(authorizationRuleSelfMapper).isExists(testTableId, testOrganizationNumber);
            verify(numberIdGenerator).generateId("tenant001");
        }
    }

    /**
     * 测试新增授权检查参数 - 参数为空
     */
    @Test
    void testAddAuthorizationRule_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.addAuthorizationRule(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增授权检查参数 - 记录已存在
     */
    @Test
    void testAddAuthorizationRule_RecordExists() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            AuthorizationRule newAuthorizationRule = new AuthorizationRule();
            newAuthorizationRule.setTableId(testTableId);
            
            beanMappingMock.when(() -> BeanMapping.copy(any(AuthorizationRuleDTO.class), eq(AuthorizationRule.class)))
                .thenReturn(newAuthorizationRule);
            
            when(authorizationRuleSelfMapper.isExists(testTableId, testOrganizationNumber))
                .thenReturn(1);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                authorizationRuleService.addAuthorizationRule(testAuthorizationRuleDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_AUTH_RULE_FAULT.getCode(), exception.getErrCode());
            verify(authorizationRuleSelfMapper).isExists(testTableId, testOrganizationNumber);
        }
    }

    /**
     * 测试新增授权检查参数 - 移除限制天数为负数
     */
    @Test
    void testAddAuthorizationRule_NegativeRemovalLimitDays() {
        // Arrange
        testAuthorizationRuleDTO.setRemovalLimitDays(-1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authorizationRuleService.addAuthorizationRule(testAuthorizationRuleDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据tableId和机构号查询 - 成功场景
     */
    @Test
    void testFindAuthorizationByTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(AuthorizationRule.class), eq(AuthorizationRuleDTO.class)))
                .thenReturn(testAuthorizationRuleDTO);
            
            when(authorizationRuleSelfMapper.selectByTableId(testTableId, testOrganizationNumber))
                .thenReturn(testAuthorizationRule);

            // Act
            AuthorizationRuleDTO result = authorizationRuleService.findAuthorizationByTableId(testTableId, testOrganizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(testTableId, result.getTableId());
            assertEquals(testOrganizationNumber, result.getOrganizationNumber());
            verify(authorizationRuleSelfMapper).selectByTableId(testTableId, testOrganizationNumber);
        }
    }

    /**
     * 测试根据tableId和机构号查询 - 记录不存在
     */
    @Test
    void testFindAuthorizationByTableId_RecordNotFound() {
        // Arrange
        when(authorizationRuleSelfMapper.selectByTableId(testTableId, testOrganizationNumber))
            .thenReturn(null);

        // Act
        AuthorizationRuleDTO result = authorizationRuleService.findAuthorizationByTableId(testTableId, testOrganizationNumber);

        // Assert
        assertNull(result);
        verify(authorizationRuleSelfMapper).selectByTableId(testTableId, testOrganizationNumber);
    }

    /**
     * 测试根据ID查询 - 成功场景
     */
    @Test
    void testFindAuthorizationById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(AuthorizationRule.class), eq(AuthorizationRuleDTO.class)))
                .thenReturn(testAuthorizationRuleDTO);
            
            when(authorizationRuleMapper.selectByPrimaryKey(testId))
                .thenReturn(testAuthorizationRule);

            // Act
            AuthorizationRuleDTO result = authorizationRuleService.findAuthorizationById(testId);

            // Assert
            assertNotNull(result);
            assertEquals(testId, result.getId());
            assertEquals(testTableId, result.getTableId());
            verify(authorizationRuleMapper).selectByPrimaryKey(testId);
        }
    }

    /**
     * 测试根据ID查询 - 记录不存在
     */
    @Test
    void testFindAuthorizationById_RecordNotFound() {
        // Arrange
        when(authorizationRuleMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act
        AuthorizationRuleDTO result = authorizationRuleService.findAuthorizationById(testId);

        // Assert
        assertNull(result);
        verify(authorizationRuleMapper).selectByPrimaryKey(testId);
    }
} 
