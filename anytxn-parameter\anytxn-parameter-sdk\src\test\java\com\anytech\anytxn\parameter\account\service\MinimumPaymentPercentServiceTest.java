﻿package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.account.service.MinimumPaymentPercentServiceImpl;
import com.anytech.anytxn.parameter.account.mapper.ParmMinimumPaymentPercentMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmMinimumPaymentPercentSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmMinimumPaymentPercent;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MinimumPaymentPercentServiceImpl 单元测试类
 * 
 * 测试 MinimumPaymentPercentServiceImpl 的所有公共方法：
 * 1. findByOrgAndTableId(String organizationNumber, String tableId) - 根据机构号和表ID查询最低还款比例参数
 * 2. addParmMinimumPaymentPercent(MinimumPaymentPercentReqDTO minimumPaymentPercentReq) - 添加最低还款比例参数
 * 3. modifyParmMinimumPaymentPercent(MinimumPaymentPercentReqDTO minimumPaymentPercentReq) - 修改最低还款比例参数
 * 4. removeParmMinimumPaymentPercent(String id) - 删除最低还款比例参数
 * 5. findAll(Integer pageNum, Integer pageSize, String tableId, String description, String billEvenDollars, String curMinPaymentPercentage, String preMinPaymentPercentage, String organizationNumber) - 分页查询最低还款比例参数
 * 6. findById(String id) - 根据ID查询最低还款比例参数
 * 7. findAllByOrgNumber(String organizationNumber) - 根据机构号查询最低还款比例参数列表
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class MinimumPaymentPercentServiceTest {

    @Mock
    private ParmMinimumPaymentPercentMapper parmMinimumPaymentPercentMapper;

    @Mock
    private ParmMinimumPaymentPercentSelfMapper parmMinimumPaymentPercentSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private MinimumPaymentPercentServiceImpl minimumPaymentPercentService;

    private MinimumPaymentPercentReqDTO testReqDTO;
    private MinimumPaymentPercentResDTO testResDTO;
    private ParmMinimumPaymentPercent testEntity;

    @BeforeEach
    void setUp() {
        // 设置测试数据 - 注意在setUp中处理OrgNumberUtils静态初始化问题
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            
            testReqDTO = new MinimumPaymentPercentReqDTO();
            testReqDTO.setId("TEST_ID_001");
            testReqDTO.setOrganizationNumber("001");
            testReqDTO.setTableId("TABLE_001");
            testReqDTO.setDescription("测试最低还款比例参数");
            testReqDTO.setBillEvenDollars("1");
            testReqDTO.setStatus("1");
            testReqDTO.setCurMinPaymentPercentage(new BigDecimal("0.05"));
            testReqDTO.setPreMinPaymentPercentage(new BigDecimal("0.10"));
            testReqDTO.setVersionNumber(1L);

            testResDTO = new MinimumPaymentPercentResDTO();
            testResDTO.setId("TEST_ID_001");
            testResDTO.setOrganizationNumber("001");
            testResDTO.setTableId("TABLE_001");
            testResDTO.setDescription("测试最低还款比例参数");
            testResDTO.setBillEvenDollars("1");
            testResDTO.setStatus("1");
            testResDTO.setCurMinPaymentPercentage(new BigDecimal("0.05"));
            testResDTO.setPreMinPaymentPercentage(new BigDecimal("0.10"));
            testResDTO.setVersionNumber(1);
            testResDTO.setCreateTime(LocalDateTime.now());
            testResDTO.setUpdateTime(LocalDateTime.now());

            testEntity = new ParmMinimumPaymentPercent();
            testEntity.setId("TEST_ID_001");
            testEntity.setOrganizationNumber("001");
            testEntity.setTableId("TABLE_001");
            testEntity.setDescription("测试最低还款比例参数");
            testEntity.setBillEvenDollars("1");
            testEntity.setStatus("1");
            testEntity.setCurMinPaymentPercentage(new BigDecimal("0.05"));
            testEntity.setPreMinPaymentPercentage(new BigDecimal("0.10"));
            testEntity.setVersionNumber(1L);
            testEntity.setCreateTime(LocalDateTime.now());
            testEntity.setUpdateTime(LocalDateTime.now());
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, MinimumPaymentPercentResDTO.class)).thenReturn(testResDTO);
            when(parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(testEntity);

            // Act
            MinimumPaymentPercentResDTO result = minimumPaymentPercentService.findByOrgAndTableId("001", "TABLE_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            assertThat(result.getTableId()).isEqualTo("TABLE_001");
            verify(parmMinimumPaymentPercentSelfMapper).selectByOrgAndTableId("001", "TABLE_001");
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound_ThrowsException() {
        // Arrange
        when(parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.findByOrgAndTableId("001", "TABLE_001");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_FAULT.getCode());
    }

    @Test
    void testAddParmMinimumPaymentPercent_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            tenantUtilsMock.when(() -> TenantUtils.getTenantId()).thenReturn("tenant_001");
            beanMappingMock.when(() -> BeanMapping.copy(testReqDTO, ParmMinimumPaymentPercent.class)).thenReturn(testEntity);
            
            when(parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(null);
            when(numberIdGenerator.generateId("tenant_001")).thenReturn(123456L);

            // Act
            ParameterCompare result = minimumPaymentPercentService.addParmMinimumPaymentPercent(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getAfter()).isNotNull();
            verify(parmMinimumPaymentPercentSelfMapper).selectByOrgAndTableId("001", "TABLE_001");
            verify(numberIdGenerator).generateId("tenant_001");
        }
    }

    @Test
    void testAddParmMinimumPaymentPercent_AlreadyExists_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            when(parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(testEntity);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                minimumPaymentPercentService.addParmMinimumPaymentPercent(testReqDTO);
            });
            
            assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_EXIST_MINIMUM_PAYMENT_PERCENT_FAULT.getCode());
        }
    }

    @Test
    void testModifyParmMinimumPaymentPercent_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testReqDTO, ParmMinimumPaymentPercent.class)).thenReturn(testEntity);
            when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

            // Act
            ParameterCompare result = minimumPaymentPercentService.modifyParmMinimumPaymentPercent(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getBefore()).isNotNull();
            assertThat(result.getAfter()).isNotNull();
            verify(parmMinimumPaymentPercentMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testModifyParmMinimumPaymentPercent_DataNotExist_ThrowsException() {
        // Arrange
        when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.modifyParmMinimumPaymentPercent(testReqDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_BY_ID_FAULT.getCode());
    }

    @Test
    void testModifyParmMinimumPaymentPercent_DifferentOrgOrTableId_ExistsConflict_ThrowsException() {
        // Arrange
        ParmMinimumPaymentPercent existingEntity = new ParmMinimumPaymentPercent();
        existingEntity.setOrganizationNumber("002"); // 不同机构号
        existingEntity.setTableId("TABLE_002"); // 不同表ID
        
        testReqDTO.setOrganizationNumber("001");
        testReqDTO.setTableId("TABLE_001");
        
        when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(existingEntity);
        when(parmMinimumPaymentPercentSelfMapper.selectByOrgAndTableId("001", "TABLE_001")).thenReturn(testEntity);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.modifyParmMinimumPaymentPercent(testReqDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_EXIST_MINIMUM_PAYMENT_PERCENT_FAULT.getCode());
    }

    @Test
    void testRemoveParmMinimumPaymentPercent_Success() {
        // Arrange
        when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

        // Act
        ParameterCompare result = minimumPaymentPercentService.removeParmMinimumPaymentPercent("TEST_ID_001");

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getBefore()).isNotNull();
        verify(parmMinimumPaymentPercentMapper).selectByPrimaryKey("TEST_ID_001");
    }

    @Test
    void testRemoveParmMinimumPaymentPercent_NullId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.removeParmMinimumPaymentPercent(null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testRemoveParmMinimumPaymentPercent_DataNotExist_ThrowsException() {
        // Arrange
        when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.removeParmMinimumPaymentPercent("TEST_ID_001");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_BY_ID_FAULT.getCode());
    }

    @Test
    void testFindAll_Success() {
        // Arrange
        List<ParmMinimumPaymentPercent> entityList = Arrays.asList(testEntity);
        List<MinimumPaymentPercentResDTO> dtoList = Arrays.asList(testResDTO);
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, MinimumPaymentPercentResDTO.class)).thenReturn(dtoList);
            
            when(parmMinimumPaymentPercentSelfMapper.selectByCondition(any(Map.class))).thenReturn(entityList);

            // Act
            PageResultDTO<MinimumPaymentPercentResDTO> result = minimumPaymentPercentService.findAll(
                1, 10, "TABLE_001", "测试", "1", "0.05", "0.10", "001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getPage()).isEqualTo(1);
            assertThat(result.getRows()).isEqualTo(10);
            verify(parmMinimumPaymentPercentSelfMapper).selectByCondition(any(Map.class));
        }
    }

    @Test
    void testFindAll_EmptyResult() {
        // Arrange
        List<ParmMinimumPaymentPercent> entityList = Collections.emptyList();
        List<MinimumPaymentPercentResDTO> dtoList = Collections.emptyList();
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, MinimumPaymentPercentResDTO.class)).thenReturn(dtoList);
            
            when(parmMinimumPaymentPercentSelfMapper.selectByCondition(any(Map.class))).thenReturn(entityList);

            // Act
            PageResultDTO<MinimumPaymentPercentResDTO> result = minimumPaymentPercentService.findAll(
                1, 10, null, null, null, null, null, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).isEmpty();
            verify(parmMinimumPaymentPercentSelfMapper).selectByCondition(any(Map.class));
        }
    }

    @Test
    void testFindAll_InvalidCurMinPaymentPercentage_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.findAll(1, 10, null, null, null, "invalid_number", null, null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION.getCode());
    }

    @Test
    void testFindAll_InvalidPreMinPaymentPercentage_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.findAll(1, 10, null, null, null, null, "invalid_number", null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION.getCode());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, MinimumPaymentPercentResDTO.class)).thenReturn(testResDTO);
            when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

            // Act
            MinimumPaymentPercentResDTO result = minimumPaymentPercentService.findById("TEST_ID_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            verify(parmMinimumPaymentPercentMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testFindById_NullId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.findById(null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    @Test
    void testFindById_NotFound_ThrowsException() {
        // Arrange
        when(parmMinimumPaymentPercentMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            minimumPaymentPercentService.findById("TEST_ID_001");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_QUERY_MINIMUM_PAYMENT_PERCENT_BY_ID_FAULT.getCode());
    }

    @Test
    void testFindAllByOrgNumber_Success() {
        // Arrange
        List<ParmMinimumPaymentPercent> entityList = Arrays.asList(testEntity);
        List<MinimumPaymentPercentResDTO> dtoList = Arrays.asList(testResDTO);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, MinimumPaymentPercentResDTO.class)).thenReturn(dtoList);
            when(parmMinimumPaymentPercentSelfMapper.findAllByOrgNumber("001")).thenReturn(entityList);

            // Act
            List<MinimumPaymentPercentResDTO> result = minimumPaymentPercentService.findAllByOrgNumber("001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getId()).isEqualTo("TEST_ID_001");
            verify(parmMinimumPaymentPercentSelfMapper).findAllByOrgNumber("001");
        }
    }

    @Test
    void testFindAllByOrgNumber_EmptyOrganizationNumber() {
        // Act
        List<MinimumPaymentPercentResDTO> result = minimumPaymentPercentService.findAllByOrgNumber("");

        // Assert
        assertThat(result).isNull();
        verify(parmMinimumPaymentPercentSelfMapper, never()).findAllByOrgNumber(anyString());
    }

    @Test
    void testFindAllByOrgNumber_NullOrganizationNumber() {
        // Act
        List<MinimumPaymentPercentResDTO> result = minimumPaymentPercentService.findAllByOrgNumber(null);

        // Assert
        assertThat(result).isNull();
        verify(parmMinimumPaymentPercentSelfMapper, never()).findAllByOrgNumber(anyString());
    }
} 
