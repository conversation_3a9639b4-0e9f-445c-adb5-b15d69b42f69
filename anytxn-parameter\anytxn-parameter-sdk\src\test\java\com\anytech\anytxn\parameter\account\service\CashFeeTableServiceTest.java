﻿package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmCashFeeTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmCashFeeTableSelfMapper;
import com.anytech.anytxn.parameter.account.service.CashFeeTableServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.CashFeeTableSearchDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmCashFeeTable;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CashFeeTableServiceImpl 单元测试类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CashFeeTableServiceTest {

    @Mock
    private ParmCashFeeTableMapper parmCashFeeTableMapper;

    @Mock
    private ParmCashFeeTableSelfMapper parmCashFeeTableSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CashFeeTableServiceImpl cashFeeTableService;

    private ParmCashFeeTable testEntity;
    private CashFeeTableReqDTO testReqDTO;
    private CashFeeTableResDTO testResDTO;
    private CashFeeTableSearchDTO testSearchDTO;

    @BeforeEach
    void setUp() {
        // 设置测试数据，在@BeforeEach中处理OrgNumberUtils静态Mock
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");
            
            testEntity = createTestEntity();
            testReqDTO = createTestReqDTO();
            testResDTO = createTestResDTO();
            testSearchDTO = createTestSearchDTO();
        }
    }

    private ParmCashFeeTable createTestEntity() {
        ParmCashFeeTable entity = new ParmCashFeeTable();
        entity.setId("TEST001");
        entity.setOrganizationNumber("001");
        entity.setTableId("T001");
        entity.setDescription("测试取现手续费");
        entity.setChargeFlag("1");
        entity.setChargeOption("1");
        entity.setChargePercent(new BigDecimal("0.025"));
        entity.setFixedAmnt(new BigDecimal("10.00"));
        entity.setMaxAmnt(new BigDecimal("50.00"));
        entity.setMinAmnt(new BigDecimal("1.00"));
        entity.setGenerateInterestOption("0");
        entity.setStatus("1");
        return entity;
    }

    private CashFeeTableReqDTO createTestReqDTO() {
        CashFeeTableReqDTO dto = new CashFeeTableReqDTO();
        dto.setId("TEST001");
        dto.setOrganizationNumber("001");
        dto.setTableId("T001");
        dto.setDescription("测试取现手续费");
        dto.setChargeFlag("1");
        dto.setChargeOption("1");
        dto.setChargePercent(new BigDecimal("0.025"));
        dto.setFixedAmnt(new BigDecimal("10.00"));
        dto.setMaxAmnt(new BigDecimal("50.00"));
        dto.setMinAmnt(new BigDecimal("1.00"));
        dto.setGenerateInterestOption("0");
        dto.setStatus("1");
        return dto;
    }

    private CashFeeTableResDTO createTestResDTO() {
        CashFeeTableResDTO dto = new CashFeeTableResDTO();
        dto.setId("TEST001");
        dto.setOrganizationNumber("001");
        dto.setTableId("T001");
        dto.setDescription("测试取现手续费");
        dto.setChargeFlag("1");
        dto.setChargeOption("1");
        dto.setChargePercent(new BigDecimal("0.025"));
        dto.setFixedAmnt(new BigDecimal("10.00"));
        dto.setMaxAmnt(new BigDecimal("50.00"));
        dto.setMinAmnt(new BigDecimal("1.00"));
        dto.setGenerateInterestOption("0");
        dto.setStatus("1");
        return dto;
    }

    private CashFeeTableSearchDTO createTestSearchDTO() {
        CashFeeTableSearchDTO dto = new CashFeeTableSearchDTO();
        dto.setOrganizationNumber("001");
        dto.setTableId("T001");
        dto.setDescription("测试");
        dto.setChargeFlag("1");
        dto.setChargeOption("1");
        return dto;
    }

    @Test
    void testAddParmFee_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copy(testReqDTO, ParmCashFeeTable.class)).thenReturn(testEntity);
            tenantUtilsMock.when(() -> TenantUtils.getTenantId()).thenReturn("tenant001");
            
            when(numberIdGenerator.generateId("tenant001")).thenReturn(123456L);
            when(parmCashFeeTableSelfMapper.selectByOrgAndTid("001", "T001")).thenReturn(null);

            // Act
            ParameterCompare result = cashFeeTableService.addParmFee(testReqDTO);

            // Assert
            assertNotNull(result);
            verify(parmCashFeeTableSelfMapper).selectByOrgAndTid("001", "T001");
            verify(numberIdGenerator).generateId("tenant001");
        }
    }

    @Test
    void testAddParmFee_AlreadyExists() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            
            when(parmCashFeeTableSelfMapper.selectByOrgAndTid("001", "T001")).thenReturn(testEntity);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> cashFeeTableService.addParmFee(testReqDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testModifyParmFee_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(testReqDTO, ParmCashFeeTable.class)).thenReturn(testEntity);
            
            when(parmCashFeeTableMapper.selectByPrimaryKey("TEST001")).thenReturn(testEntity);

            // Act
            ParameterCompare result = cashFeeTableService.modifyParmFee(testReqDTO);

            // Assert
            assertNotNull(result);
            verify(parmCashFeeTableMapper).selectByPrimaryKey("TEST001");
        }
    }

    @Test
    void testModifyParmFee_NotFound() {
        // Arrange
        when(parmCashFeeTableMapper.selectByPrimaryKey("TEST001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cashFeeTableService.modifyParmFee(testReqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveParmFee_Success() {
        // Arrange
        when(parmCashFeeTableMapper.selectByPrimaryKey("TEST001")).thenReturn(testEntity);

        // Act
        ParameterCompare result = cashFeeTableService.removeParmFee("TEST001");

        // Assert
        assertNotNull(result);
        verify(parmCashFeeTableMapper).selectByPrimaryKey("TEST001");
    }

    @Test
    void testRemoveParmFee_NotFound() {
        // Arrange
        when(parmCashFeeTableMapper.selectByPrimaryKey("TEST001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cashFeeTableService.removeParmFee("TEST001"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, CashFeeTableResDTO.class)).thenReturn(testResDTO);
            
            when(parmCashFeeTableMapper.selectByPrimaryKey("TEST001")).thenReturn(testEntity);

            // Act
            CashFeeTableResDTO result = cashFeeTableService.findById("TEST001");

            // Assert
            assertNotNull(result);
            assertEquals("TEST001", result.getId());
            verify(parmCashFeeTableMapper).selectByPrimaryKey("TEST001");
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, CashFeeTableResDTO.class)).thenReturn(testResDTO);
            
            when(parmCashFeeTableSelfMapper.selectByOrgAndTid("001", "T001")).thenReturn(testEntity);

            // Act
            CashFeeTableResDTO result = cashFeeTableService.findByOrgAndTableId("001", "T001");

            // Assert
            assertNotNull(result);
            assertEquals("T001", result.getTableId());
            verify(parmCashFeeTableSelfMapper).selectByOrgAndTid("001", "T001");
        }
    }
} 
