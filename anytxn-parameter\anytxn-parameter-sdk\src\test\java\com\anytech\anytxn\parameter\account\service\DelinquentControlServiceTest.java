﻿package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlSelfMapper;
import com.anytech.anytxn.parameter.account.service.DelinquentControlServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelinquentControl;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DelinquentControlServiceTest 测试类
 * 测试延滞控制参数服务的核心功能
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DelinquentControlServiceTest {

    @Mock
    private ParmDelinquentControlMapper parmDelinquentControlMapper;
    
    @Mock
    private ParmDelinquentControlSelfMapper parmDelinquentControlSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private DelinquentControlServiceImpl delinquentControlService;

    private DelinquentControlReqDTO testReqDTO;
    private DelinquentControlResDTO testResDTO;
    private ParmDelinquentControl testEntity;

    @BeforeEach
    void setUp() {
        // 在@BeforeEach中使用try-with-resources确保OrgNumberUtils静态Mock生效
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            // 创建测试数据
            testReqDTO = new DelinquentControlReqDTO();
            testReqDTO.setId(1L);
            testReqDTO.setTableId("DELINQ001");
            testReqDTO.setCycleDue(30);
            testReqDTO.setBlockCode("BLK001");
            testReqDTO.setDescription("延滞控制参数测试");
            testReqDTO.setStatus("1");
            testReqDTO.setVersionNumber(1L);
            
            testResDTO = new DelinquentControlResDTO();
            testResDTO.setId(1L);
            testResDTO.setTableId("DELINQ001");
            testResDTO.setCycleDue(30);
            testResDTO.setBlockCode("BLK001");
            testResDTO.setDescription("延滞控制参数测试");
            testResDTO.setStatus("1");
            testResDTO.setVersionNumber(1L);
            testResDTO.setCreateTime(LocalDateTime.now());
            testResDTO.setUpdateTime(LocalDateTime.now());
            
            testEntity = new ParmDelinquentControl();
            testEntity.setId(1L);
            testEntity.setOrganizationNumber("001");
            testEntity.setTableId("DELINQ001");
            testEntity.setCycleDue(30);
            testEntity.setBlockCode("BLK001");
            testEntity.setDescription("延滞控制参数测试");
            testEntity.setStatus("1");
            testEntity.setVersionNumber(1L);
            testEntity.setCreateTime(LocalDateTime.now());
            testEntity.setUpdateTime(LocalDateTime.now());
        }
    }

    /**
     * 测试查询所有延滞控制参数 - 成功场景
     * 测试方法：DelinquentControlServiceImpl.findAll(Integer, Integer)
     */
    @Test
    void testFindAll_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(parmDelinquentControlSelfMapper.selectAll(false, "001")).thenReturn(List.of(testEntity));
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(DelinquentControlResDTO.class)))
                    .thenReturn(List.of(testResDTO));

            // Act
            PageResultDTO<DelinquentControlResDTO> result = delinquentControlService.findAll(1, 10);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getData().get(0).getId()).isEqualTo(1L);
            verify(parmDelinquentControlSelfMapper).selectAll(false, "001");
        }
    }

    /**
     * 测试查询所有延滞控制参数 - 空结果异常
     * 测试方法：DelinquentControlServiceImpl.findAll(Integer, Integer)
     */
    @Test
    void testFindAll_EmptyResult_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(parmDelinquentControlSelfMapper.selectAll(false, "001")).thenReturn(Collections.emptyList());

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> delinquentControlService.findAll(1, 10));

            assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode());
            verify(parmDelinquentControlSelfMapper).selectAll(false, "001");
        }
    }

    /**
     * 测试根据ID查询延滞控制参数 - 成功场景
     * 测试方法：DelinquentControlServiceImpl.findById(Long)
     */
    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmDelinquentControlMapper.selectByPrimaryKey(1L)).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmDelinquentControl.class), eq(DelinquentControlResDTO.class)))
                    .thenReturn(testResDTO);

            // Act
            DelinquentControlResDTO result = delinquentControlService.findById(1L);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
            assertThat(result.getTableId()).isEqualTo("DELINQ001");
            verify(parmDelinquentControlMapper).selectByPrimaryKey(1L);
        }
    }

    /**
     * 测试根据ID查询延滞控制参数 - ID为空异常
     * 测试方法：DelinquentControlServiceImpl.findById(Long)
     */
    @Test
    void testFindById_NullId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> delinquentControlService.findById(null));

        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    /**
     * 测试添加延滞控制参数 - 成功场景
     * 测试方法：DelinquentControlServiceImpl.addDelinquentControl(DelinquentControlReqDTO)
     */
    @Test
    void testAddDelinquentControl_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(parmDelinquentControlSelfMapper.selectByOrgAndTableIdAndCycleDue("001", "DELINQ001", 30))
                    .thenReturn(Collections.emptyList());
            beanMappingMock.when(() -> BeanMapping.copy(any(DelinquentControlReqDTO.class), eq(ParmDelinquentControl.class)))
                    .thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmDelinquentControl.class), eq(DelinquentControlResDTO.class)))
                    .thenReturn(testResDTO);
            when(numberIdGenerator.generateId("tenant001")).thenReturn(12345L);
            when(parmDelinquentControlMapper.insertSelective(any(ParmDelinquentControl.class))).thenReturn(1);

            // Act
            DelinquentControlResDTO result = delinquentControlService.addDelinquentControl(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
            verify(parmDelinquentControlSelfMapper).selectByOrgAndTableIdAndCycleDue("001", "DELINQ001", 30);
            verify(parmDelinquentControlMapper).insertSelective(any(ParmDelinquentControl.class));
        }
    }

    /**
     * 测试修改延滞控制参数 - 成功场景
     * 测试方法：DelinquentControlServiceImpl.modifyDelinquentControl(DelinquentControlReqDTO)
     */
    @Test
    void testModifyDelinquentControl_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmDelinquentControlMapper.selectByPrimaryKey(1L)).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(DelinquentControlReqDTO.class), eq(ParmDelinquentControl.class)))
                    .thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmDelinquentControl.class), eq(DelinquentControlResDTO.class)))
                    .thenReturn(testResDTO);
            when(parmDelinquentControlMapper.updateByPrimaryKeySelective(any(ParmDelinquentControl.class))).thenReturn(1);

            // Act
            DelinquentControlResDTO result = delinquentControlService.modifyDelinquentControl(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
            verify(parmDelinquentControlMapper).selectByPrimaryKey(1L);
            verify(parmDelinquentControlMapper).updateByPrimaryKeySelective(any(ParmDelinquentControl.class));
        }
    }

    /**
     * 测试删除延滞控制参数 - 成功场景
     * 测试方法：DelinquentControlServiceImpl.removeDelinquentControl(Long)
     */
    @Test
    void testRemoveDelinquentControl_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(parmDelinquentControlMapper.selectByPrimaryKey(1L)).thenReturn(testEntity);
            when(parmDelinquentControlMapper.deleteByPrimaryKey(1L)).thenReturn(1);

            // Act
            Boolean result = delinquentControlService.removeDelinquentControl(1L);

            // Assert
            assertThat(result).isTrue();
            verify(parmDelinquentControlMapper).selectByPrimaryKey(1L);
            verify(parmDelinquentControlMapper).deleteByPrimaryKey(1L);
        }
    }

    /**
     * 测试根据机构号和表ID查询延滞控制参数 - 成功场景
     * 测试方法：DelinquentControlServiceImpl.findByOrgNumTableIdCycleDue(String, String, Integer)
     */
    @Test
    void testFindByOrgNumTableIdCycleDue_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmDelinquentControlSelfMapper.selectByOrgNumTableIdCycleDue("001", "DELINQ001", 30))
                    .thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmDelinquentControl.class), eq(DelinquentControlResDTO.class)))
                    .thenReturn(testResDTO);

            // Act
            DelinquentControlResDTO result = delinquentControlService.findByOrgNumTableIdCycleDue("001", "DELINQ001", 30);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
            assertThat(result.getTableId()).isEqualTo("DELINQ001");
            verify(parmDelinquentControlSelfMapper).selectByOrgNumTableIdCycleDue("001", "DELINQ001", 30);
        }
    }
} 
