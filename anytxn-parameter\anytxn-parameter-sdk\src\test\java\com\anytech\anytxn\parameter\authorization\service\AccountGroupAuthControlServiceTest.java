﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupAuthControlDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupAuthDefineDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupRespDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.AccountGroupAuthControl;
import com.anytech.anytxn.parameter.base.authorization.domain.model.AccountGroupAuthDefine;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.service.AccountGroupAuthControlServiceImpl;
import com.anytech.anytxn.parameter.authorization.mapper.AccountGroupAuthControlMapper;
import com.anytech.anytxn.parameter.authorization.mapper.AccountGroupAuthDefineMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AccountGroupAuthControlService单元测试类
 * 
 * 测试11个核心方法：
 * 1. findPage - 分页查询
 * 2. getAccountGroupControlInfo - 根据账产品组标识获取控制详情
 * 3. getAccountGroupControlInfoById - 根据ID获取控制详情
 * 4. modifyAccountGroupAuthControlInfo - 修改账产品组信息
 * 5. addAccountGroupControlInfo - 新增控制规则
 * 6. updateAccountGroupControlInfo - 更新控制规则
 * 7. exitAccountGroupAuthDefine - 检查是否存在账产品编号
 * 8. deleteAccountGroupControlInfo - 删除控制记录
 * 9. deleteAccountGroupControlDefine - 删除账产品标识记录
 * 10. getEffectiveAccountGroupAuthControl - 获取有效的产品组编号记录
 * 11. addAccountGroupAuthControlInfo - 定义账产品组信息
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AccountGroupAuthControlServiceTest {

    @Mock
    private AccountGroupAuthControlMapper accountGroupAuthControlMapper;

    @Mock
    private AccountGroupAuthDefineMapper accountGroupAuthDefineMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private AccountGroupAuthControlServiceImpl accountGroupAuthControlService;

    private AccountGroupAuthDefineDTO accountGroupAuthDefineDTO;
    private AccountGroupAuthDefine accountGroupAuthDefine;
    private AccountGroupAuthControlDTO accountGroupAuthControlDTO;
    private AccountGroupAuthControl accountGroupAuthControl;
    private AccountGroupRespDTO accountGroupRespDTO;

    @BeforeEach
    void setUp() {
        // Mock OrgNumberUtils 避免构造函数中的空指针异常
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            // 创建AccountGroupAuthDefine测试数据
            accountGroupAuthDefine = new AccountGroupAuthDefine();
            accountGroupAuthDefine.setId("1");
            accountGroupAuthDefine.setAcctProductGroup("G001");
            accountGroupAuthDefine.setDescription("测试账产品组");
            accountGroupAuthDefine.setStatus("1");
            accountGroupAuthDefine.setOrganizationNumber("1001");
            accountGroupAuthDefine.setVersionNumber(1L);

            // 创建AccountGroupAuthDefineDTO测试数据
            accountGroupAuthDefineDTO = new AccountGroupAuthDefineDTO();
            accountGroupAuthDefineDTO.setId("1");
            accountGroupAuthDefineDTO.setAcctProductGroup("G001");
            accountGroupAuthDefineDTO.setDescription("测试账产品组");
            accountGroupAuthDefineDTO.setStatus("1");
            accountGroupAuthDefineDTO.setOrganizationNumber("1001");
            accountGroupAuthDefineDTO.setVersionNumber(1L);

            // 创建AccountGroupAuthControl测试数据
            accountGroupAuthControl = new AccountGroupAuthControl();
            accountGroupAuthControl.setId("1");
            accountGroupAuthControl.setAcctProductGroup("G001");
            accountGroupAuthControl.setAcctProductCode("P001");
            accountGroupAuthControl.setPrimarySupplymentaryInd("0");
            accountGroupAuthControl.setAcctStatusCheckInd("1");
            accountGroupAuthControl.setAcctBlockCodeCheckInd("1");
            accountGroupAuthControl.setAcctCycleDueCheckInd("1");
            accountGroupAuthControl.setOrganizationNumber("1001");

            // 创建AccountGroupAuthControlDTO测试数据
            accountGroupAuthControlDTO = new AccountGroupAuthControlDTO();
            accountGroupAuthControlDTO.setId("1");
            accountGroupAuthControlDTO.setAcctProductGroup("G001");
            accountGroupAuthControlDTO.setAcctProductCode("P001");
            accountGroupAuthControlDTO.setPrimarySupplymentaryInd("0");
            accountGroupAuthControlDTO.setAcctStatusCheckInd("1");
            accountGroupAuthControlDTO.setAcctBlockCodeCheckInd("1");
            accountGroupAuthControlDTO.setAcctCycleDueCheckInd("1");
            accountGroupAuthControlDTO.setOrganizationNumber("1001");

            // 创建AccountGroupRespDTO测试数据
            accountGroupRespDTO = new AccountGroupRespDTO();
            accountGroupRespDTO.setAccountGroupAuthDefine(accountGroupAuthDefineDTO);
            accountGroupRespDTO.setAccountGroupAuthControls(Arrays.asList(accountGroupAuthControlDTO));
            accountGroupRespDTO.setId("1");
            accountGroupRespDTO.setVersionNumber(1L);
        }
    }

    // ==================== findPage 测试 ====================

    @Test
    void testFindPage_Success() {
        // Arrange
        List<AccountGroupAuthDefine> accountGroupAuthDefines = Arrays.asList(accountGroupAuthDefine);
        when(accountGroupAuthDefineMapper.selectByCondition("1001", "G001", "测试")).thenReturn(accountGroupAuthDefines);

        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg("1001")).thenReturn("1001");

            // Act
            PageResultDTO<AccountGroupAuthDefineDTO> result = accountGroupAuthControlService.findPage(1, 10, "G001", "测试", "1001");

            // Assert
            assertNotNull(result);
            verify(accountGroupAuthDefineMapper).selectByCondition("1001", "G001", "测试");
        }
    }

    @Test
    void testFindPage_EmptyResult() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByCondition("1001", null, null)).thenReturn(new ArrayList<>());

        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg("1001")).thenReturn("1001");

            // Act
            PageResultDTO<AccountGroupAuthDefineDTO> result = accountGroupAuthControlService.findPage(1, 10, null, null, "1001");

            // Assert
            assertNotNull(result);
            verify(accountGroupAuthDefineMapper).selectByCondition("1001", null, null);
        }
    }

    // ==================== getAccountGroupControlInfo 测试 ====================

    @Test
    void testGetAccountGroupControlInfo_Success() {
        // Arrange
        List<AccountGroupAuthControl> authControls = Arrays.asList(accountGroupAuthControl);

        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(accountGroupAuthDefine);
        when(accountGroupAuthControlMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(authControls);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copy(accountGroupAuthDefine, AccountGroupAuthDefineDTO.class))
                    .thenReturn(accountGroupAuthDefineDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(authControls, AccountGroupAuthControlDTO.class))
                    .thenReturn(Arrays.asList(accountGroupAuthControlDTO));

            // Act
            AnyTxnHttpResponse<AccountGroupRespDTO> result = accountGroupAuthControlService.getAccountGroupControlInfo("G001", "1001");

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals("G001", result.getData().getAccountGroupAuthDefine().getAcctProductGroup());
            assertEquals(1, result.getData().getAccountGroupAuthControls().size());
        }
    }

    @Test
    void testGetAccountGroupControlInfo_EmptyAcctProductGroup() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.getAccountGroupControlInfo("", "1001"));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testGetAccountGroupControlInfo_DataNotExist() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.getAccountGroupControlInfo("G001", "1001"));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    // ==================== getAccountGroupControlInfoById 测试 ====================

    @Test
    void testGetAccountGroupControlInfoById_Success() {
        // Arrange
        List<AccountGroupAuthControl> authControls = Arrays.asList(accountGroupAuthControl);

        when(accountGroupAuthDefineMapper.selectByPrimaryKey("1")).thenReturn(accountGroupAuthDefine);
        when(accountGroupAuthControlMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(authControls);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copy(accountGroupAuthDefine, AccountGroupAuthDefineDTO.class))
                    .thenReturn(accountGroupAuthDefineDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(authControls, AccountGroupAuthControlDTO.class))
                    .thenReturn(Arrays.asList(accountGroupAuthControlDTO));

            // Act
            AccountGroupRespDTO result = accountGroupAuthControlService.getAccountGroupControlInfoById("1");

            // Assert
            assertNotNull(result);
            assertEquals("G001", result.getAccountGroupAuthDefine().getAcctProductGroup());
            assertEquals(1, result.getAccountGroupAuthControls().size());
        }
    }

    @Test
    void testGetAccountGroupControlInfoById_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.getAccountGroupControlInfoById(""));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testGetAccountGroupControlInfoById_DataNotExist() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByPrimaryKey("1")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.getAccountGroupControlInfoById("1"));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    // ==================== getEffectiveAccountGroupAuthControl 测试 ====================

    @Test
    void testGetEffectiveAccountGroupAuthControl_Success() {
        // Arrange
        List<AccountGroupAuthControl> authControls = Arrays.asList(accountGroupAuthControl);

        when(accountGroupAuthDefineMapper.selectByAcctProductGroupEffective("1001", "G001")).thenReturn(accountGroupAuthDefine);
        when(accountGroupAuthControlMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(authControls);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copyList(authControls, AccountGroupAuthControlDTO.class))
                    .thenReturn(Arrays.asList(accountGroupAuthControlDTO));

            // Act
            List<AccountGroupAuthControlDTO> result = accountGroupAuthControlService.getEffectiveAccountGroupAuthControl("1001", "G001");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("P001", result.get(0).getAcctProductCode());
        }
    }

    @Test
    void testGetEffectiveAccountGroupAuthControl_NotFound() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroupEffective("1001", "G001")).thenReturn(null);

        // Act
        List<AccountGroupAuthControlDTO> result = accountGroupAuthControlService.getEffectiveAccountGroupAuthControl("1001", "G001");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // ==================== exitAccountGroupAuthDefine 测试 ====================

    @Test
    void testExitAccountGroupAuthDefine_Exists() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(accountGroupAuthDefine);

        // Act
        Boolean result = accountGroupAuthControlService.exitAccountGroupAuthDefine("G001", "1001");

        // Assert
        assertTrue(result);
        verify(accountGroupAuthDefineMapper).selectByAcctProductGroup("1001", "G001");
    }

    @Test
    void testExitAccountGroupAuthDefine_NotExists() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(null);

        // Act
        Boolean result = accountGroupAuthControlService.exitAccountGroupAuthDefine("G001", "1001");

        // Assert
        assertFalse(result);
        verify(accountGroupAuthDefineMapper).selectByAcctProductGroup("1001", "G001");
    }

    // ==================== deleteAccountGroupControlInfo 测试 ====================

    @Test
    void testDeleteAccountGroupControlInfo_Success() {
        // Arrange
        accountGroupAuthControl.setPrimarySupplymentaryInd("1"); // 非主检查记录
        when(accountGroupAuthControlMapper.selectByPrimaryKey("1")).thenReturn(accountGroupAuthControl);
        when(accountGroupAuthControlMapper.deleteByPrimaryKey("1")).thenReturn(1);

        // Act
        AnyTxnHttpResponse<Boolean> result = accountGroupAuthControlService.deleteAccountGroupControlInfo(accountGroupAuthControlDTO);

        // Assert
        assertNotNull(result);
        assertTrue(result.getData());
        verify(accountGroupAuthControlMapper).deleteByPrimaryKey("1");
    }

    @Test
    void testDeleteAccountGroupControlInfo_EmptyId() {
        // Arrange
        accountGroupAuthControlDTO.setId("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.deleteAccountGroupControlInfo(accountGroupAuthControlDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testDeleteAccountGroupControlInfo_DataNotExist() {
        // Arrange
        when(accountGroupAuthControlMapper.selectByPrimaryKey("1")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.deleteAccountGroupControlInfo(accountGroupAuthControlDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    @Test
    void testDeleteAccountGroupControlInfo_MasterCheckRecord() {
        // Arrange
        accountGroupAuthControl.setPrimarySupplymentaryInd("0"); // 主检查记录，不能删除
        when(accountGroupAuthControlMapper.selectByPrimaryKey("1")).thenReturn(accountGroupAuthControl);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.deleteAccountGroupControlInfo(accountGroupAuthControlDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DARA_DELETE_ERROR.getCode(), exception.getErrCode());
    }

    // ==================== addAccountGroupControlInfo 测试 ====================

    @Test
    void testAddAccountGroupControlInfo_Success() {
        // Arrange
        try (MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("1");
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456L);
            
            AccountGroupAuthControl mockControl = mock(AccountGroupAuthControl.class);
            mockBeanMapping.when(() -> BeanMapping.copy(accountGroupAuthControlDTO, AccountGroupAuthControl.class))
                    .thenReturn(mockControl);
            when(accountGroupAuthControlMapper.insert(any(AccountGroupAuthControl.class))).thenReturn(1);

            // Act
            Integer result = accountGroupAuthControlService.addAccountGroupControlInfo(accountGroupAuthControlDTO);

            // Assert
            assertEquals(1, result);
        }
    }

    // ==================== updateAccountGroupControlInfo 测试 ====================

    @Test
    void testUpdateAccountGroupControlInfo_Success() {
        // Act
        Integer result = accountGroupAuthControlService.updateAccountGroupControlInfo(accountGroupAuthControlDTO);

        // Assert
        assertNull(result); // 方法返回null
    }

    // ==================== addAccountGroupAuthControlInfo 测试 ====================

    @Test
    void testAddAccountGroupAuthControlInfo_Success() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(null);
        when(numberIdGenerator.generateId(anyString())).thenReturn(123456L);

        try (MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("1");
            com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupDTO mockAccountGroupDTO = 
                    mock(com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupDTO.class);
            mockBeanMapping.when(() -> BeanMapping.copy(any(), eq(com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupDTO.class)))
                    .thenReturn(mockAccountGroupDTO);

            // Act
            ParameterCompare result = accountGroupAuthControlService.addAccountGroupAuthControlInfo(accountGroupRespDTO);

            // Assert
            assertNotNull(result);
        }
    }

    @Test
    void testAddAccountGroupAuthControlInfo_AlreadyExists() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(accountGroupAuthDefine);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.addAccountGroupAuthControlInfo(accountGroupRespDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAddAccountGroupAuthControlInfo_EmptyControlList() {
        // Arrange
        accountGroupRespDTO.setAccountGroupAuthControls(new ArrayList<>());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.addAccountGroupAuthControlInfo(accountGroupRespDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== modifyAccountGroupAuthControlInfo 测试 ====================

    @Test
    void testModifyAccountGroupAuthControlInfo_Success() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(accountGroupAuthDefine);
        when(accountGroupAuthDefineMapper.selectByPrimaryKey("1")).thenReturn(accountGroupAuthDefine);
        when(accountGroupAuthControlMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(Arrays.asList(accountGroupAuthControl));

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copy(accountGroupAuthDefine, AccountGroupAuthDefineDTO.class))
                    .thenReturn(accountGroupAuthDefineDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(any(), eq(AccountGroupAuthControlDTO.class)))
                    .thenReturn(Arrays.asList(accountGroupAuthControlDTO));

            // Act
            ParameterCompare result = accountGroupAuthControlService.modifyAccountGroupAuthControlInfo(accountGroupRespDTO);

            // Assert
            assertNotNull(result);
        }
    }

    @Test
    void testModifyAccountGroupAuthControlInfo_DataNotExists() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.modifyAccountGroupAuthControlInfo(accountGroupRespDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    // ==================== deleteAccountGroupControlDefine 测试 ====================

    @Test
    void testDeleteAccountGroupControlDefine_Success() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(accountGroupAuthDefine);

        // Act
        ParameterCompare result = accountGroupAuthControlService.deleteAccountGroupControlDefine("G001", "1001");

        // Assert
        assertNotNull(result);
        verify(accountGroupAuthDefineMapper).selectByAcctProductGroup("1001", "G001");
    }

    @Test
    void testDeleteAccountGroupControlDefine_DataNotExists() {
        // Arrange
        when(accountGroupAuthDefineMapper.selectByAcctProductGroup("1001", "G001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> accountGroupAuthControlService.deleteAccountGroupControlDefine("G001", "1001"));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }
} 
