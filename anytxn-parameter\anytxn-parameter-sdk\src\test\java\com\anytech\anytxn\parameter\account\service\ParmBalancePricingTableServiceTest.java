﻿package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmBalancePricingTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBalancePricingTableSelfMapper;
import com.anytech.anytxn.parameter.account.service.ParmBalancePricingTableServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmBalancePricingTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBalancePricingTable;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmBalancePricingTableService单元测试类
 * 测试ParmBalancePricingTableServiceImpl中的所有公共方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@ExtendWith(MockitoExtension.class)
class ParmBalancePricingTableServiceTest {

    @Mock
    private ParmBalancePricingTableMapper parmBalancePricingTableMapper;

    @Mock
    private ParmBalancePricingTableSelfMapper parmBalancePricingTableSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private ParmBalancePricingTableServiceImpl balancePricingTableService;

    private ParmBalancePricingTableResDTO mockResDTO;
    private ParmBalancePricingTable mockParmBalancePricingTable;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);

        // 构建测试用的ParmBalancePricingTableResDTO
        mockResDTO = new ParmBalancePricingTableResDTO();
        mockResDTO.setId("BALANCE001");
        mockResDTO.setTableId("TABLE001");
        mockResDTO.setOrganizationNumber("ORG001");
        mockResDTO.setDescription("测试余额定价参数");
        mockResDTO.setTransactionTypeCode("TXN001");
        mockResDTO.setStatus("1");
        mockResDTO.setMinimumPaymentTableId("MIN001");
        mockResDTO.setInterestBearingTableId("INT001");
        mockResDTO.setInterestSettlementTableId("SET001");
        mockResDTO.setBalanceInwardTransferTableId("TRANS001");

        // 构建测试用的ParmBalancePricingTable
        mockParmBalancePricingTable = new ParmBalancePricingTable();
        mockParmBalancePricingTable.setId("BALANCE001");
        mockParmBalancePricingTable.setTableId("TABLE001");
        mockParmBalancePricingTable.setOrganizationNumber("ORG001");
        mockParmBalancePricingTable.setDescription("测试余额定价参数");
        mockParmBalancePricingTable.setTransactionTypeCode("TXN001");
        mockParmBalancePricingTable.setStatus("1");
        mockParmBalancePricingTable.setMinimumPaymentTableId("MIN001");
        mockParmBalancePricingTable.setInterestBearingTableId("INT001");
        mockParmBalancePricingTable.setInterestSettlementTableId("SET001");
        mockParmBalancePricingTable.setBalanceInwardTransferTableId("TRANS001");
        mockParmBalancePricingTable.setVersionNumber(1L);
        mockParmBalancePricingTable.setCreateTime(LocalDateTime.now());
        mockParmBalancePricingTable.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 测试 add 方法 - 成功路径
     */
    @Test
    void testAdd_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            // Given
            when(parmBalancePricingTableSelfMapper.isExists(anyString(), anyString(), anyString()))
                    .thenReturn(0);
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            beanMapping.when(() -> BeanMapping.copy(mockResDTO, ParmBalancePricingTable.class))
                    .thenReturn(mockParmBalancePricingTable);

            // When
            ParameterCompare result = balancePricingTableService.add(mockResDTO);

            // Then
            assertNotNull(result);
            verify(parmBalancePricingTableSelfMapper).isExists(anyString(), anyString(), anyString());
            verify(numberIdGenerator).generateId(anyString());
        }
    }

    /**
     * 测试 add 方法 - 数据已存在异常
     */
    @Test
    void testAdd_AlreadyExists() {
        // Given
        when(parmBalancePricingTableSelfMapper.isExists(anyString(), anyString(), anyString()))
                .thenReturn(1);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.add(mockResDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_BALANCE_PRICING_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add 方法 - 业务异常
     */
    @Test
    void testAdd_BusinessException() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            // Given
            when(parmBalancePricingTableSelfMapper.isExists(anyString(), anyString(), anyString()))
                    .thenReturn(0);
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            beanMapping.when(() -> BeanMapping.copy(mockResDTO, ParmBalancePricingTable.class))
                    .thenThrow(new RuntimeException("业务异常"));

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                balancePricingTableService.add(mockResDTO);
            });
            assertEquals(AnyTxnParameterRespCodeEnum.D_ADD_PARM_BALANCE_PRICING_TABLE_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 modify 方法 - 成功路径
     */
    @Test
    void testModify_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            when(parmBalancePricingTableMapper.selectByPrimaryKey(mockResDTO.getId()))
                    .thenReturn(mockParmBalancePricingTable);
            when(parmBalancePricingTableSelfMapper.selectByIndex(anyString(), anyString(), anyString()))
                    .thenReturn(null);
            beanMapping.when(() -> BeanMapping.copy(mockResDTO, ParmBalancePricingTable.class))
                    .thenReturn(mockParmBalancePricingTable);

            // When
            ParameterCompare result = balancePricingTableService.modify(mockResDTO);

            // Then
            assertNotNull(result);
            verify(parmBalancePricingTableMapper).selectByPrimaryKey(mockResDTO.getId());
        }
    }

    /**
     * 测试 modify 方法 - ID为null异常
     */
    @Test
    void testModify_NullId() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            mockResDTO.setId(null);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                balancePricingTableService.modify(mockResDTO);
            });
            assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 modify 方法 - 数据未找到异常
     */
    @Test
    void testModify_NotFound() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            when(parmBalancePricingTableMapper.selectByPrimaryKey(mockResDTO.getId()))
                    .thenReturn(null);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                balancePricingTableService.modify(mockResDTO);
            });
            assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 modify 方法 - 数据已存在异常
     */
    @Test
    void testModify_AlreadyExists() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("ORG001");
            when(parmBalancePricingTableMapper.selectByPrimaryKey(mockResDTO.getId()))
                    .thenReturn(mockParmBalancePricingTable);
            
            ParmBalancePricingTable existingTable = new ParmBalancePricingTable();
            existingTable.setId("DIFFERENT_ID");
            when(parmBalancePricingTableSelfMapper.selectByIndex(anyString(), anyString(), anyString()))
                    .thenReturn(existingTable);
            beanMapping.when(() -> BeanMapping.copy(mockResDTO, ParmBalancePricingTable.class))
                    .thenReturn(mockParmBalancePricingTable);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                balancePricingTableService.modify(mockResDTO);
            });
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_BALANCE_PRICING_TABLE_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 remove 方法 - 成功路径
     */
    @Test
    void testRemove_Success() {
        // Given
        String id = "BALANCE001";
        when(parmBalancePricingTableMapper.selectByPrimaryKey(id)).thenReturn(mockParmBalancePricingTable);

        // When
        ParameterCompare result = balancePricingTableService.remove(id);

        // Then
        assertNotNull(result);
        verify(parmBalancePricingTableMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove 方法 - ID为null异常
     */
    @Test
    void testRemove_NullId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.remove(null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove 方法 - ID为空字符串异常
     */
    @Test
    void testRemove_EmptyId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.remove("");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove 方法 - 数据未找到异常
     */
    @Test
    void testRemove_NotFound() {
        // Given
        String id = "BALANCE001";
        when(parmBalancePricingTableMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.remove(id);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByIndex 方法 - 成功路径
     */
    @Test
    void testFindByIndex_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String organizationNumber = "ORG001";
            String tableId = "TABLE001";
            String transactionTypeCode = "TXN001";

            when(parmBalancePricingTableSelfMapper.selectByIndex(organizationNumber, tableId, transactionTypeCode))
                    .thenReturn(mockParmBalancePricingTable);
            beanMapping.when(() -> BeanMapping.copy(mockParmBalancePricingTable, ParmBalancePricingTableResDTO.class))
                    .thenReturn(mockResDTO);

            // When
            ParmBalancePricingTableResDTO result = balancePricingTableService.findByIndex(organizationNumber, tableId, transactionTypeCode);

            // Then
            assertNotNull(result);
            assertEquals("BALANCE001", result.getId());
            verify(parmBalancePricingTableSelfMapper).selectByIndex(organizationNumber, tableId, transactionTypeCode);
        }
    }

    /**
     * 测试 findByIndex 方法 - organizationNumber为null异常
     */
    @Test
    void testFindByIndex_NullOrganizationNumber() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.findByIndex(null, "TABLE001", "TXN001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByIndex 方法 - tableId为null异常
     */
    @Test
    void testFindByIndex_NullTableId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.findByIndex("ORG001", null, "TXN001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByIndex 方法 - transactionTypeCode为null异常
     */
    @Test
    void testFindByIndex_NullTransactionTypeCode() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.findByIndex("ORG001", "TABLE001", null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByIndex 方法 - 数据未找到异常
     */
    @Test
    void testFindByIndex_NotFound() {
        // Given
        String organizationNumber = "ORG001";
        String tableId = "TABLE001";
        String transactionTypeCode = "TXN001";

        when(parmBalancePricingTableSelfMapper.selectByIndex(organizationNumber, tableId, transactionTypeCode))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.findByIndex(organizationNumber, tableId, transactionTypeCode);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findPage 方法 - 成功路径
     */
    @Test
    void testFindPage_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            Page mockPage = new Page<>(1, 10);
            mockPage.setTotal(1);
            List<ParmBalancePricingTable> mockList = Arrays.asList(mockParmBalancePricingTable);
            List<ParmBalancePricingTableResDTO> mockResList = Arrays.asList(mockResDTO);

            when(parmBalancePricingTableSelfMapper.selectByCondition(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, ParmBalancePricingTableResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<ParmBalancePricingTableResDTO> result = balancePricingTableService.findPage(1, 10, mockResDTO);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            verify(parmBalancePricingTableSelfMapper).selectByCondition(any());
        }
    }

    /**
     * 测试 findPage 方法 - 空参数
     */
    @Test
    void testFindPage_NullRequest() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            Page mockPage = new Page<>(1, 10);
            mockPage.setTotal(0);
            List<ParmBalancePricingTable> mockList = Collections.emptyList();
            List<ParmBalancePricingTableResDTO> mockResList = Collections.emptyList();

            when(parmBalancePricingTableSelfMapper.selectByCondition(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, ParmBalancePricingTableResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<ParmBalancePricingTableResDTO> result = balancePricingTableService.findPage(1, 10, null);

            // Then
            assertNotNull(result);
            verify(parmBalancePricingTableSelfMapper).selectByCondition(any());
        }
    }

    /**
     * 测试 findPage 方法 - 空organizationNumber使用默认值
     */
    @Test
    void testFindPage_EmptyOrganizationNumber() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {

            // Given
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            mockResDTO.setOrganizationNumber("");

            Page mockPage = new Page<>(1, 10);
            mockPage.setTotal(0);
            List<ParmBalancePricingTable> mockList = Collections.emptyList();
            List<ParmBalancePricingTableResDTO> mockResList = Collections.emptyList();

            when(parmBalancePricingTableSelfMapper.selectByCondition(any())).thenReturn(mockList);
            beanMapping.when(() -> BeanMapping.copyList(mockList, ParmBalancePricingTableResDTO.class))
                    .thenReturn(mockResList);

            // When
            PageResultDTO<ParmBalancePricingTableResDTO> result = balancePricingTableService.findPage(1, 10, mockResDTO);

            // Then
            assertNotNull(result);
            verify(parmBalancePricingTableSelfMapper).selectByCondition(any());
        }
    }

    /**
     * 测试 findById 方法 - 成功路径
     */
    @Test
    void testFindById_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String id = "BALANCE001";
            when(parmBalancePricingTableMapper.selectByPrimaryKey(id)).thenReturn(mockParmBalancePricingTable);
            beanMapping.when(() -> BeanMapping.copy(mockParmBalancePricingTable, ParmBalancePricingTableResDTO.class))
                    .thenReturn(mockResDTO);

            // When
            ParmBalancePricingTableResDTO result = balancePricingTableService.findById(id);

            // Then
            assertNotNull(result);
            assertEquals("BALANCE001", result.getId());
            verify(parmBalancePricingTableMapper).selectByPrimaryKey(id);
        }
    }

    /**
     * 测试 findById 方法 - ID为null异常
     */
    @Test
    void testFindById_NullId() {
        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.findById(null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findById 方法 - 数据未找到异常
     */
    @Test
    void testFindById_NotFound() {
        // Given
        String id = "BALANCE001";
        when(parmBalancePricingTableMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            balancePricingTableService.findById(id);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_BALANCE_PRICING_TABLE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }
} 
