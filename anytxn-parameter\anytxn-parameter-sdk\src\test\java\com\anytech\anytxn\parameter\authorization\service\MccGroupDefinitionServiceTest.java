﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.service.MccGroupDefinitionServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MccGroupDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmMccGroupDefinition;
import com.anytech.anytxn.parameter.base.authorization.service.IMccGroupDetailService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDefinitionMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.MccGroupDefinitionSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MccGroupDefinitionService 单元测试类
 * 
 * 测试商户类别群服务的核心业务逻辑
 * 
 * <AUTHOR> Generator
 * @date 2025-06-25
 */
@ExtendWith(MockitoExtension.class)
class MccGroupDefinitionServiceTest {

    @Mock
    private MccGroupDefinitionMapper mccGroupDefinitionMapper;

    @Mock
    private MccGroupDefinitionSelfMapper mccGroupDefinitionSelfMapper;

    @Mock
    private IMccGroupDetailService mccGroupDetailService;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private MccGroupDefinitionServiceImpl mccGroupDefinitionService;

    private String testOrganizationNumber;
    private Long testId;
    private String testMccGroup;
    private String testDescription;
    private String testStatus;
    private ParmMccGroupDefinition testParmMccGroupDefinition;
    private MccGroupDefinitionDTO testMccGroupDefinitionDTO;
    private List<MccGroupDetailDTO> testMccGroupDetailDTOList;

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据
        testOrganizationNumber = "001";
        testId = 123L;
        testMccGroup = "GROUP001";
        testDescription = "测试商户类别群";
        testStatus = "1";

        // 初始化OrgNumberUtils静态实例
        try {
            Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
            orgNumberUtilField.setAccessible(true);
            OrgNumberUtils mockOrgNumberUtil = mock(OrgNumberUtils.class);
            orgNumberUtilField.set(null, mockOrgNumberUtil);
            when(mockOrgNumberUtil.getBatchOrg()).thenReturn(testOrganizationNumber);
        } catch (Exception e) {
            // 如果反射失败，可以忽略
        }

        // 创建测试的ParmMccGroupDefinition对象
        testParmMccGroupDefinition = new ParmMccGroupDefinition();
        testParmMccGroupDefinition.setId(testId);
        testParmMccGroupDefinition.setMccGroup(testMccGroup);
        testParmMccGroupDefinition.setDescription(testDescription);
        testParmMccGroupDefinition.setStatus(testStatus);
        testParmMccGroupDefinition.setOrganizationNumber(testOrganizationNumber);
        testParmMccGroupDefinition.setCreateTime(LocalDateTime.now());
        testParmMccGroupDefinition.setUpdateTime(LocalDateTime.now());
        testParmMccGroupDefinition.setUpdateBy("admin");
        testParmMccGroupDefinition.setVersionNumber(1L);

        // 创建测试的MccGroupDefinitionDTO对象
        testMccGroupDefinitionDTO = new MccGroupDefinitionDTO();
        testMccGroupDefinitionDTO.setId(testId);
        testMccGroupDefinitionDTO.setMccGroup(testMccGroup);
        testMccGroupDefinitionDTO.setDescription(testDescription);
        testMccGroupDefinitionDTO.setStatus(testStatus);
        testMccGroupDefinitionDTO.setOrganizationNumber(testOrganizationNumber);
        testMccGroupDefinitionDTO.setCreateTime(LocalDateTime.now());
        testMccGroupDefinitionDTO.setUpdateTime(LocalDateTime.now());
        testMccGroupDefinitionDTO.setUpdateBy("admin");
        testMccGroupDefinitionDTO.setVersionNumber(1L);

        // 创建测试的MccGroupDetailDTO列表
        testMccGroupDetailDTOList = new ArrayList<>();
        MccGroupDetailDTO detailDTO = new MccGroupDetailDTO();
        detailDTO.setId(1L);
        detailDTO.setMccGroup(testMccGroup);
        detailDTO.setMccCde("1234");
        detailDTO.setOrganizationNumber(testOrganizationNumber);
        testMccGroupDetailDTOList.add(detailDTO);
        testMccGroupDefinitionDTO.setMccGroupDetailDTOList(testMccGroupDetailDTOList);
    }

    /**
     * 测试分页查询商户类别群列表 - 成功场景
     */
    @Test
    void testFindListMccGroupDefinition_Success() {
        // Arrange
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            Page<Object> mockPage = new Page<>();
            mockPage.setTotal(1);
            mockPage.setPages(1);
            
            pageHelperMock.when(() -> PageHelper.startPage(1, 10)).thenReturn(mockPage);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            // Mock BeanMapping to avoid BaseParam constructor issues
            beanMappingMock.when(() -> BeanMapping.copyList(any(), eq(MccGroupDefinitionDTO.class)))
                    .thenReturn(Arrays.asList(testMccGroupDefinitionDTO));
            
            when(mccGroupDefinitionSelfMapper.selectByCondition(
                    eq(testOrganizationNumber), eq(testMccGroup), eq(testDescription)))
                    .thenReturn(Arrays.asList(testParmMccGroupDefinition));

            // Act
            PageResultDTO<MccGroupDefinitionDTO> result = mccGroupDefinitionService
                    .findListMccGroupDefinition(1, 10, testMccGroup, testDescription, testOrganizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals(testMccGroup, result.getData().get(0).getMccGroup());
            verify(mccGroupDefinitionSelfMapper).selectByCondition(testOrganizationNumber, testMccGroup, testDescription);
        }
    }

    /**
     * 测试分页查询商户类别群列表 - 查询为空时使用默认机构号
     */
    @Test
    void testFindListMccGroupDefinition_EmptyOrgNumber() {
        // Arrange
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            Page<Object> mockPage = new Page<>();
            mockPage.setTotal(0);
            mockPage.setPages(0);
            
            pageHelperMock.when(() -> PageHelper.startPage(1, 10)).thenReturn(mockPage);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            // Mock BeanMapping to avoid BaseParam constructor issues
            beanMappingMock.when(() -> BeanMapping.copyList(any(), eq(MccGroupDefinitionDTO.class)))
                    .thenReturn(Collections.emptyList());
            
            when(mccGroupDefinitionSelfMapper.selectByCondition(
                    eq(testOrganizationNumber), isNull(), isNull()))
                    .thenReturn(Collections.emptyList());

            // Act
            PageResultDTO<MccGroupDefinitionDTO> result = mccGroupDefinitionService
                    .findListMccGroupDefinition(1, 10, null, null, testOrganizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getTotal());
            assertTrue(result.getData() == null || result.getData().isEmpty());
        }
    }

    /**
     * 测试分页查询商户类别群列表 - 数据库异常
     */
    @Test
    void testFindListMccGroupDefinition_DatabaseException() {
        // Arrange
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            Page<Object> mockPage = new Page<>();
            pageHelperMock.when(() -> PageHelper.startPage(1, 10)).thenReturn(mockPage);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            // Mock BeanMapping to avoid BaseParam constructor issues
            beanMappingMock.when(() -> BeanMapping.copyList(any(), eq(MccGroupDefinitionDTO.class)))
                    .thenReturn(Collections.emptyList());
            
            when(mccGroupDefinitionSelfMapper.selectByCondition(any(), any(), any()))
                    .thenThrow(new RuntimeException("Database error"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                mccGroupDefinitionService.findListMccGroupDefinition(1, 10, null, null, testOrganizationNumber);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_MCC_GROUP_DEFINITION_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试根据主键查询商户类别群 - 成功场景
     */
    @Test
    void testFindMccGroupDefinition_Success() {
        // Arrange
        when(mccGroupDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(testParmMccGroupDefinition);

        // Act
        MccGroupDefinitionDTO result = mccGroupDefinitionService.findMccGroupDefinition(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testId, result.getId());
        assertEquals(testMccGroup, result.getMccGroup());
        verify(mccGroupDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据主键查询商户类别群 - 记录不存在
     */
    @Test
    void testFindMccGroupDefinition_NotFound() {
        // Arrange
        when(mccGroupDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDefinitionService.findMccGroupDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(mccGroupDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据主键查询商户类别群 - 数据库异常
     */
    @Test
    void testFindMccGroupDefinition_DatabaseException() {
        // Arrange
        when(mccGroupDefinitionMapper.selectByPrimaryKey(testId))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDefinitionService.findMccGroupDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改商户类别群 - 成功场景
     */
    @Test
    void testModifyMccGroupDefinition_Success() {
        // Arrange
        when(mccGroupDefinitionMapper.updateByPrimaryKeySelective(any(ParmMccGroupDefinition.class)))
                .thenReturn(1);

        // Act
        Boolean result = mccGroupDefinitionService.modifyMccGroupDefinition(testMccGroupDefinitionDTO);

        // Assert
        assertTrue(result);
        verify(mccGroupDefinitionMapper).updateByPrimaryKeySelective(any(ParmMccGroupDefinition.class));
    }

    /**
     * 测试修改商户类别群 - 数据库异常
     */
    @Test
    void testModifyMccGroupDefinition_DatabaseException() {
        // Arrange
        when(mccGroupDefinitionMapper.updateByPrimaryKeySelective(any(ParmMccGroupDefinition.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDefinitionService.modifyMccGroupDefinition(testMccGroupDefinitionDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_UPDATE_MCC_GROUP_DEFINITION_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除商户类别群 - 成功场景
     */
    @Test
    void testRemoveMccGroupDefinition_Success() {
        // Arrange
        when(mccGroupDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(testParmMccGroupDefinition);
        when(mccGroupDefinitionMapper.deleteByPrimaryKey(testId))
                .thenReturn(1);

        // Act
        int result = mccGroupDefinitionService.removeMccGroupDefinition(testId);

        // Assert
        assertEquals(1, result);
        verify(mccGroupDefinitionMapper).selectByPrimaryKey(testId);
        verify(mccGroupDefinitionMapper).deleteByPrimaryKey(testId);
    }

    /**
     * 测试删除商户类别群 - 记录不存在
     */
    @Test
    void testRemoveMccGroupDefinition_NotFound() {
        // Arrange
        when(mccGroupDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDefinitionService.removeMccGroupDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_MCC_GROUP_DEFINITION_NULL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(mccGroupDefinitionMapper).selectByPrimaryKey(testId);
        verify(mccGroupDefinitionMapper, never()).deleteByPrimaryKey(testId);
    }

    /**
     * 测试删除商户类别群 - 数据库异常
     */
    @Test
    void testRemoveMccGroupDefinition_DatabaseException() {
        // Arrange
        when(mccGroupDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(testParmMccGroupDefinition);
        when(mccGroupDefinitionMapper.deleteByPrimaryKey(testId))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDefinitionService.removeMccGroupDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_MCC_GROUP_DEFINITION_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增商户类别群 - 成功场景
     */
    @Test
    void testAddMccGroupDefinition_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(any(MccGroupDefinitionDTO.class), eq(ParmMccGroupDefinition.class)))
                    .thenReturn(testParmMccGroupDefinition);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(mccGroupDefinitionSelfMapper.isExists(testMccGroup, testOrganizationNumber))
                    .thenReturn(0);
            when(numberIdGenerator.generateId("tenant001"))
                    .thenReturn(testId);
            when(mccGroupDefinitionMapper.insertSelective(any(ParmMccGroupDefinition.class)))
                    .thenReturn(1);

            // Act
            Boolean result = mccGroupDefinitionService.addMccGroupDefinition(testMccGroupDefinitionDTO);

            // Assert
            assertTrue(result);
            verify(mccGroupDefinitionSelfMapper).isExists(testMccGroup, testOrganizationNumber);
            verify(numberIdGenerator).generateId("tenant001");
            verify(mccGroupDefinitionMapper).insertSelective(any(ParmMccGroupDefinition.class));
        }
    }

    /**
     * 测试新增商户类别群 - 参数为空
     */
    @Test
    void testAddMccGroupDefinition_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            mccGroupDefinitionService.addMccGroupDefinition(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增商户类别群 - 记录已存在
     */
    @Test
    void testAddMccGroupDefinition_AlreadyExists() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(any(MccGroupDefinitionDTO.class), eq(ParmMccGroupDefinition.class)))
                    .thenReturn(testParmMccGroupDefinition);
            
            when(mccGroupDefinitionSelfMapper.isExists(testMccGroup, testOrganizationNumber))
                    .thenReturn(1);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                mccGroupDefinitionService.addMccGroupDefinition(testMccGroupDefinitionDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_MCC_GROUP_DEFINITION_FAULT.getCode(), exception.getErrCode());
            verify(mccGroupDefinitionSelfMapper).isExists(testMccGroup, testOrganizationNumber);
        }
    }

    /**
     * 测试新增商户类别群 - 数据库异常
     */
    @Test
    void testAddMccGroupDefinition_DatabaseException() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            beanMappingMock.when(() -> BeanMapping.copy(any(MccGroupDefinitionDTO.class), eq(ParmMccGroupDefinition.class)))
                    .thenReturn(testParmMccGroupDefinition);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(mccGroupDefinitionSelfMapper.isExists(testMccGroup, testOrganizationNumber))
                    .thenReturn(0);
            when(numberIdGenerator.generateId("tenant001"))
                    .thenReturn(testId);
            when(mccGroupDefinitionMapper.insertSelective(any(ParmMccGroupDefinition.class)))
                    .thenThrow(new RuntimeException("Database error"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                mccGroupDefinitionService.addMccGroupDefinition(testMccGroupDefinitionDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_MCC_GROUP_DEFINITION_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试获取所有商户类别群 - 成功场景
     */
    @Test
    void testGetMccGroups_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(mccGroupDefinitionSelfMapper.selectAll(true, testOrganizationNumber))
                    .thenReturn(Arrays.asList(testParmMccGroupDefinition));

            // Act
            List<Map<String, String>> result = mccGroupDefinitionService.getMccGroups();

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testMccGroup, result.get(0).get("value"));
            assertEquals(testDescription, result.get(0).get("label"));
            verify(mccGroupDefinitionSelfMapper).selectAll(true, testOrganizationNumber);
        }
    }
} 
