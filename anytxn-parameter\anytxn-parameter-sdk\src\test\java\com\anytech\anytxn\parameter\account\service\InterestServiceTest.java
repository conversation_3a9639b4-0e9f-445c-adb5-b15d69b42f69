﻿package com.anytech.anytxn.parameter.account.service;

import com.github.pagehelper.Page;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.account.service.InterestServiceImpl;
import com.anytech.anytxn.parameter.account.mapper.ParmInterestMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmInterestSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisMapper;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSearchDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmInterest;
import com.anytech.anytxn.parameter.base.account.domain.model.PmInterestHis;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InterestServiceTest 测试类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class InterestServiceTest {

    @Mock
    private ParmInterestMapper parmInterestMapper;
    
    @Mock
    private ParmInterestSelfMapper parmInterestSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;
    
    @Mock
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    
    @Mock
    private PmInterestHisMapper pmInterestHisMapper;
    
    @Mock
    private PmInterestHisSelfMapper pmInterestHisSelfMapper;

    @InjectMocks
    private InterestServiceImpl interestService;

    private ParmInterest parmInterest;
    private InterestReqDTO interestReqDTO;
    private InterestResDTO interestResDTO;
    private InterestSearchDTO interestSearchDTO;
    private ParmOrganizationInfo parmOrganizationInfo;

    @BeforeEach
    void setUp() {
        // 在@BeforeEach中使用try-with-resources确保静态Mock生效
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("1001");
            
            // 创建测试实体对象
            parmInterest = new ParmInterest();
            parmInterest.setId("1");
            parmInterest.setOrganizationNumber("1001");
            parmInterest.setTableId("INT001");
            parmInterest.setDescription("测试利率参数");
            parmInterest.setInterestType("0");
            parmInterest.setBaseRate(new BigDecimal("0.05"));
            parmInterest.setBaseRatePercent(new BigDecimal("5.00"));
            parmInterest.setGraceOption("1");
            parmInterest.setInterestOnInterestOption("0");
            parmInterest.setMonthBase("0");
            parmInterest.setYearBase("0");
            parmInterest.setPaymentBackdateOption("0");
            parmInterest.setStartDateOption("0");
            parmInterest.setWavieOption("0");
            parmInterest.setStatus("1");
            parmInterest.setCreateTime(LocalDateTime.now());
            parmInterest.setUpdateTime(LocalDateTime.now());
            parmInterest.setVersionNumber(1L);

            // 创建测试请求DTO对象
            interestReqDTO = new InterestReqDTO();
            interestReqDTO.setOrganizationNumber("1001");
            interestReqDTO.setTableId("INT001");
            interestReqDTO.setDescription("测试利率参数");
            interestReqDTO.setInterestType("0");
            interestReqDTO.setBaseRate(new BigDecimal("0.05"));
            interestReqDTO.setBaseRatePercent(new BigDecimal("5.00"));
            interestReqDTO.setGraceOption("1");
            interestReqDTO.setInterestOnInterestOption("0");
            interestReqDTO.setMonthBase("0");
            interestReqDTO.setYearBase("0");
            interestReqDTO.setPaymentBackdateOption("0");
            interestReqDTO.setStartDateOption("0");
            interestReqDTO.setWavieOption("0");

            // 创建测试响应DTO对象
            interestResDTO = new InterestResDTO();
            interestResDTO.setId("1");
            interestResDTO.setOrganizationNumber("1001");
            interestResDTO.setTableId("INT001");
            interestResDTO.setDescription("测试利率参数");
            interestResDTO.setInterestType("0");
            interestResDTO.setBaseRate(new BigDecimal("0.05"));
            interestResDTO.setBaseRatePercent(new BigDecimal("5.00"));
            interestResDTO.setGraceOption("1");
            interestResDTO.setInterestOnInterestOption("0");
            interestResDTO.setMonthBase("0");
            interestResDTO.setYearBase("0");
            interestResDTO.setPaymentBackdateOption("0");
            interestResDTO.setStartDateOption("0");
            interestResDTO.setWavieOption("0");
            interestResDTO.setStatus("1");

            // 创建测试搜索DTO对象
            interestSearchDTO = new InterestSearchDTO();
            interestSearchDTO.setOrganizationNumber("1001");
            interestSearchDTO.setTableId("INT001");
            interestSearchDTO.setDescription("测试");
            interestSearchDTO.setBaseRate("0.05");
            interestSearchDTO.setGraceOption("1");
            interestSearchDTO.setInterestOnInterestOption("0");
            interestSearchDTO.setStartDateOption("0");
            interestSearchDTO.setWavieOption("0");

            // 创建机构信息对象
            parmOrganizationInfo = new ParmOrganizationInfo();
            parmOrganizationInfo.setOrganizationNumber("1001");
            parmOrganizationInfo.setAccruedThruDay(LocalDate.now());
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        when(parmInterestSelfMapper.isExists("1001", "INT001")).thenReturn(parmInterest);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmInterest, InterestResDTO.class))
                          .thenReturn(interestResDTO);

            // Act
            InterestResDTO result = interestService.findByOrgAndTableId("1001", "INT001");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("1001", result.getOrganizationNumber());
            assertEquals("INT001", result.getTableId());
            verify(parmInterestSelfMapper).isExists("1001", "INT001");
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        when(parmInterestSelfMapper.isExists("1001", "INT001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> interestService.findByOrgAndTableId("1001", "INT001"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_FAULT.getCode(), exception.getErrCode());
        verify(parmInterestSelfMapper).isExists("1001", "INT001");
    }

    @Test
    void testFindById_Success() {
        // Arrange
        when(parmInterestMapper.selectByPrimaryKey("1")).thenReturn(parmInterest);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmInterest, InterestResDTO.class))
                          .thenReturn(interestResDTO);

            // Act
            InterestResDTO result = interestService.findById("1");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("INT001", result.getTableId());
            verify(parmInterestMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testFindById_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> interestService.findById(null));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(parmInterestMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        when(parmInterestMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> interestService.findById("999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmInterestMapper).selectByPrimaryKey("999");
    }

    @Test
    void testFindByStatus_Success() {
        // Arrange
        List<ParmInterest> parmInterests = Arrays.asList(parmInterest);
        when(parmInterestSelfMapper.selectByStatus("1", "1001")).thenReturn(parmInterests);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copyList(parmInterests, InterestResDTO.class))
                          .thenReturn(Arrays.asList(interestResDTO));

            // Act
            List<InterestResDTO> result = interestService.findByStatus("1");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("1", result.get(0).getId());
            verify(parmInterestSelfMapper).selectByStatus("1", "1001");
        }
    }

    @Test
    void testFindByStatus_EmptyStatus() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> interestService.findByStatus(""));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(parmInterestSelfMapper, never()).selectByStatus(any(), any());
    }

    @Test
    void testFindByStatus_EmptyResult() {
        // Arrange
        when(parmInterestSelfMapper.selectByStatus("1", "1001")).thenReturn(Collections.emptyList());
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> interestService.findByStatus("1"));
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_STATUS_FAULT.getCode(), exception.getErrCode());
            verify(parmInterestSelfMapper).selectByStatus("1", "1001");
        }
    }

    @Test
    void testAddParmInterest_Success() {
        // Arrange
        when(parmInterestSelfMapper.isExists("1001", "INT001")).thenReturn(null);
        when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class)) {
            
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg("1001")).thenReturn("1001");
            mockTenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");
            mockBeanMapping.when(() -> BeanMapping.copy(interestReqDTO, ParmInterest.class))
                          .thenReturn(parmInterest);

            // Act
            ParameterCompare result = interestService.addParmInterest(interestReqDTO);

            // Assert
            assertNotNull(result);
            verify(parmInterestSelfMapper).isExists("1001", "INT001");
            verify(numberIdGenerator).generateId("tenant1");
        }
    }

    @Test
    void testAddParmInterest_AlreadyExists() {
        // Arrange
        when(parmInterestSelfMapper.isExists("1001", "INT001")).thenReturn(parmInterest);
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg("1001")).thenReturn("1001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> interestService.addParmInterest(interestReqDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_INTEREST_FAULT.getCode(), exception.getErrCode());
            verify(parmInterestSelfMapper).isExists("1001", "INT001");
        }
    }

    @Test
    void testRemoveParmInterest_Success() {
        // Arrange
        when(parmInterestMapper.selectByPrimaryKey("1")).thenReturn(parmInterest);

        // Act
        ParameterCompare result = interestService.removeParmInterest("1");

        // Assert
        assertNotNull(result);
        verify(parmInterestMapper).selectByPrimaryKey("1");
    }

    @Test
    void testRemoveParmInterest_NotFound() {
        // Arrange
        when(parmInterestMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> interestService.removeParmInterest("999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_INTEREST_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmInterestMapper).selectByPrimaryKey("999");
    }

    @Test
    void testFindAll_Success() {
        // Arrange
        Page<ParmInterest> page = new Page<>(1, 10);
        page.setTotal(1);
        page.setPages(1);
        
        List<ParmInterest> parmInterests = Arrays.asList(parmInterest);
        when(parmInterestSelfMapper.selectAll(false, "1001")).thenReturn(parmInterests);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copyList(parmInterests, InterestResDTO.class))
                          .thenReturn(Arrays.asList(interestResDTO));

            // Mock PageHelper.startPage返回值
            when(parmInterestSelfMapper.selectAll(false, "1001")).thenReturn(parmInterests);

            // Act
            PageResultDTO<InterestResDTO> result = interestService.findAll(1, 10);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals("1", result.getData().get(0).getId());
            verify(parmInterestSelfMapper).selectAll(false, "1001");
        }
    }

    @Test
    void testFindAll_EmptyResult() {
        // Arrange
        when(parmInterestSelfMapper.selectAll(false, "1001")).thenReturn(Collections.emptyList());
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> interestService.findAll(1, 10));
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(), exception.getErrCode());
            verify(parmInterestSelfMapper).selectAll(false, "1001");
        }
    }

    @Test
    void testFindByCondition_Success() {
        // Arrange
        Page<ParmInterest> page = new Page<>(1, 10);
        page.setTotal(1);
        page.setPages(1);
        
        List<ParmInterest> parmInterests = Arrays.asList(parmInterest);
        when(parmInterestSelfMapper.selectByCondition(any(InterestSearchDTO.class))).thenReturn(parmInterests);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copyList(parmInterests, InterestResDTO.class))
                          .thenReturn(Arrays.asList(interestResDTO));

            // Act
            PageResultDTO<InterestResDTO> result = interestService.findByCondition(1, 10, interestSearchDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals("1", result.getData().get(0).getId());
            verify(parmInterestSelfMapper).selectByCondition(any(InterestSearchDTO.class));
        }
    }

    @Test
    void testFindByCondition_WithNullSearch() {
        // Arrange
        Page<ParmInterest> page = new Page<>(1, 10);
        page.setTotal(0);
        page.setPages(0);
        
        when(parmInterestSelfMapper.selectByCondition(any(InterestSearchDTO.class))).thenReturn(Collections.emptyList());
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copyList(Collections.emptyList(), InterestResDTO.class))
                          .thenReturn(Collections.emptyList());

            // Act
            PageResultDTO<InterestResDTO> result = interestService.findByCondition(1, 10, null);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(0, result.getData().size());
            verify(parmInterestSelfMapper).selectByCondition(any(InterestSearchDTO.class));
        }
    }

    @Test
    void testFindByCondition_InvalidBaseRate() {
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            // Arrange
            InterestSearchDTO invalidSearchDTO = new InterestSearchDTO();
            invalidSearchDTO.setOrganizationNumber("1001");
            invalidSearchDTO.setBaseRate("invalid_number");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> interestService.findByCondition(1, 10, invalidSearchDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION.getCode(), exception.getErrCode());
        }
    }
} 
