﻿package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.ParmSafetyLockMapper;
import com.anytech.anytxn.parameter.authorization.service.SafeLockServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.SafeLockDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.SafeLockDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmSafetyLock;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SafeLockServiceImpl 单元测试类
 * 测试安全开关规则服务的所有业务逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SafeLockService 单元测试")
class SafeLockServiceTest {

    @Mock
    private ParmSafetyLockMapper parmSafetyLockMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private SafeLockServiceImpl safeLockService;

    private SafeLockDTO testSafeLockDTO;
    private SafeLockDetailDTO testSafeLockDetailDTO;
    private ParmSafetyLock testParmSafetyLock;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态实例，解决BaseParam构造函数问题
        try {
            java.lang.reflect.Field field = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
            field.setAccessible(true);
            field.set(null, new OrgNumberUtils());
        } catch (Exception e) {
            // 忽略反射异常
        }
        
        // 创建测试用的SafeLockDetailDTO
        testSafeLockDetailDTO = new SafeLockDetailDTO();
        testSafeLockDetailDTO.setId("1");
        testSafeLockDetailDTO.setAuthTxnType("01");
        testSafeLockDetailDTO.setAuthTxnCode("001");
        testSafeLockDetailDTO.setCheckCurrency("USD");
        testSafeLockDetailDTO.setOnOff("1");
        testSafeLockDetailDTO.setVersionNumber(1L);

        // 创建测试用的SafeLockDTO
        testSafeLockDTO = new SafeLockDTO();
        testSafeLockDTO.setId("1");
        testSafeLockDTO.setCardProductCode("TESTPROD");
        testSafeLockDTO.setOrganizationNumber("0001");
        testSafeLockDTO.setAuthTxnType("01");
        testSafeLockDTO.setAuthTxnCode("001");
        testSafeLockDTO.setCheckCurrency("USD");
        testSafeLockDTO.setOnOff("1");
        testSafeLockDTO.setDescription("测试安全开关");
        testSafeLockDTO.setStatus("1");
        testSafeLockDTO.setCreateTime(LocalDateTime.now());
        testSafeLockDTO.setUpdateTime(LocalDateTime.now());
        testSafeLockDTO.setUpdateBy("testUser");
        testSafeLockDTO.setVersionNumber(1L);
        testSafeLockDTO.setSfeLockDetailDtos(Collections.singletonList(testSafeLockDetailDTO));

        // 创建测试用的ParmSafetyLock
        testParmSafetyLock = new ParmSafetyLock();
        testParmSafetyLock.setId("1");
        testParmSafetyLock.setCardProductCode("TESTPROD");
        testParmSafetyLock.setOrganizationNumber("0001");
        testParmSafetyLock.setAuthTxnType("01");
        testParmSafetyLock.setAuthTxnCode("001");
        testParmSafetyLock.setCheckCurrency("USD");
        testParmSafetyLock.setOnOff("1");
        testParmSafetyLock.setDescription("测试安全开关");
        testParmSafetyLock.setStatus("1");
        testParmSafetyLock.setCreateTime(LocalDateTime.now());
        testParmSafetyLock.setUpdateTime(LocalDateTime.now());
        testParmSafetyLock.setUpdateBy("testUser");
        testParmSafetyLock.setVersionNumber(1L);
    }

    @Test
    @DisplayName("测试分页查询安全开关规则列表 - 成功场景")
    void testFindListSafeLock_Success() {
        // Arrange
        Integer page = 1;
        Integer rows = 10;
        String cardProductCode = "TESTPROD";
        String organizationNumber = "0001";
        
        List<ParmSafetyLock> parmSafetyLockList = Collections.singletonList(testParmSafetyLock);
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            when(parmSafetyLockMapper.selectByCondition(cardProductCode, organizationNumber))
                    .thenReturn(parmSafetyLockList);

            // Act
            PageResultDTO<SafeLockDTO> result = safeLockService.findListSafeLock(page, rows, cardProductCode, organizationNumber);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getPage()).isEqualTo(page);
            assertThat(result.getRows()).isEqualTo(rows);
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getData().get(0).getCardProductCode()).isEqualTo("TESTPROD");
            
            verify(parmSafetyLockMapper).selectByCondition(cardProductCode, organizationNumber);
        }
    }

    @Test
    @DisplayName("测试分页查询安全开关规则列表 - 数据库异常")
    void testFindListSafeLock_DatabaseException() {
        // Arrange
        Integer page = 1;
        Integer rows = 10;
        String cardProductCode = "TESTPROD";
        String organizationNumber = "0001";
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            when(parmSafetyLockMapper.selectByCondition(cardProductCode, organizationNumber))
                    .thenThrow(new RuntimeException("数据库连接异常"));

                    // Act & Assert
        assertThatThrownBy(() -> safeLockService.findListSafeLock(page, rows, cardProductCode, organizationNumber))
                .isInstanceOf(AnyTxnParameterException.class);
            
            verify(parmSafetyLockMapper).selectByCondition(cardProductCode, organizationNumber);
        }
    }

    @Test
    @DisplayName("测试新增安全开关规则 - 成功场景")
    void testAddSafeLock_Success() {
        // Arrange
        when(parmSafetyLockMapper.selectByUniqueCondition(
                testSafeLockDTO.getOrganizationNumber(),
                testSafeLockDTO.getCardProductCode(),
                testSafeLockDetailDTO.getAuthTxnType(),
                testSafeLockDetailDTO.getAuthTxnCode(),
                testSafeLockDetailDTO.getCheckCurrency()))
                .thenReturn(null);

        // Act
        ParameterCompare result = safeLockService.addSafeLock(testSafeLockDTO);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getMainParmId()).isEqualTo("TESTPROD");
        assertThat(result.getAfter()).isNotNull();
        
        verify(parmSafetyLockMapper).selectByUniqueCondition(
                testSafeLockDTO.getOrganizationNumber(),
                testSafeLockDTO.getCardProductCode(),
                testSafeLockDetailDTO.getAuthTxnType(),
                testSafeLockDetailDTO.getAuthTxnCode(),
                testSafeLockDetailDTO.getCheckCurrency());
    }

    @Test
    @DisplayName("测试新增安全开关规则 - 参数为空异常")
    void testAddSafeLock_ParameterEmptyException() {
        // Act & Assert
        assertThatThrownBy(() -> safeLockService.addSafeLock(null))
                .isInstanceOf(AnyTxnParameterException.class);
    }

    @Test
    @DisplayName("测试新增安全开关规则 - 数据已存在异常")
    void testAddSafeLock_DataAlreadyExistsException() {
        // Arrange
        when(parmSafetyLockMapper.selectByUniqueCondition(
                testSafeLockDTO.getOrganizationNumber(),
                testSafeLockDTO.getCardProductCode(),
                testSafeLockDetailDTO.getAuthTxnType(),
                testSafeLockDetailDTO.getAuthTxnCode(),
                testSafeLockDetailDTO.getCheckCurrency()))
                .thenReturn(testParmSafetyLock);

        // Act & Assert
        assertThatThrownBy(() -> safeLockService.addSafeLock(testSafeLockDTO))
                .isInstanceOf(AnyTxnParameterException.class);
        
        verify(parmSafetyLockMapper).selectByUniqueCondition(
                testSafeLockDTO.getOrganizationNumber(),
                testSafeLockDTO.getCardProductCode(),
                testSafeLockDetailDTO.getAuthTxnType(),
                testSafeLockDetailDTO.getAuthTxnCode(),
                testSafeLockDetailDTO.getCheckCurrency());
    }

    @Test
    @DisplayName("测试修改安全开关规则 - 成功场景")
    void testModifySafeLock_Success() {
        // Arrange
        List<ParmSafetyLock> parmSafetyLockList = Collections.singletonList(testParmSafetyLock);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmSafetyLock.class), eq(SafeLockDetailDTO.class)))
                    .thenReturn(testSafeLockDetailDTO);
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(SafeLockDetailDTO.class)))
                    .thenReturn(Collections.singletonList(testSafeLockDetailDTO));
            
            try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
                orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
                
                when(parmSafetyLockMapper.selectByCardProductCode(testSafeLockDTO.getCardProductCode(), testSafeLockDTO.getOrganizationNumber()))
                        .thenReturn(parmSafetyLockList);

                // Act
                ParameterCompare result = safeLockService.modifySafeLock(testSafeLockDTO);

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getMainParmId()).isEqualTo("TESTPROD");
                assertThat(result.getAfter()).isNotNull();
                assertThat(result.getBefore()).isNotNull();
                
                verify(parmSafetyLockMapper).selectByCardProductCode(testSafeLockDTO.getCardProductCode(), testSafeLockDTO.getOrganizationNumber());
            }
        }
    }

    @Test
    @DisplayName("测试修改安全开关规则 - 参数为空异常")
    void testModifySafeLock_ParameterEmptyException() {
        // Act & Assert
        assertThatThrownBy(() -> safeLockService.modifySafeLock(null))
                .isInstanceOf(AnyTxnParameterException.class);
    }

    @Test
    @DisplayName("测试删除安全开关规则 - 成功场景")
    void testRemoveSafeLock_Success() {
        // Arrange
        String cardProductCode = "TESTPROD";
        String organizationNumber = "0001";
        List<ParmSafetyLock> parmSafetyLockList = Collections.singletonList(testParmSafetyLock);
        
        when(parmSafetyLockMapper.selectByCardProductCode(cardProductCode, organizationNumber))
                .thenReturn(parmSafetyLockList);

        // Act
        ParameterCompare result = safeLockService.removeSafeLock(cardProductCode, organizationNumber);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getMainParmId()).isEqualTo(cardProductCode);
        assertThat(result.getBefore()).isNotNull();
        
        verify(parmSafetyLockMapper).selectByCardProductCode(cardProductCode, organizationNumber);
    }

    @Test
    @DisplayName("测试删除安全开关规则 - 数据不存在异常")
    void testRemoveSafeLock_DataNotExistsException() {
        // Arrange
        String cardProductCode = "TESTPROD";
        String organizationNumber = "0001";
        
        when(parmSafetyLockMapper.selectByCardProductCode(cardProductCode, organizationNumber))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        assertThatThrownBy(() -> safeLockService.removeSafeLock(cardProductCode, organizationNumber))
                .isInstanceOf(AnyTxnParameterException.class);
        
        verify(parmSafetyLockMapper).selectByCardProductCode(cardProductCode, organizationNumber);
    }

    @Test
    @DisplayName("测试根据ID删除安全开关规则 - 成功场景")
    void testRemoveSafeLockById_Success() {
        // Arrange
        String id = "1";
        
        when(parmSafetyLockMapper.selectByPrimaryKey(id)).thenReturn(testParmSafetyLock);
        when(parmSafetyLockMapper.deleteByPrimaryKey(id)).thenReturn(1);

        // Act
        safeLockService.removeSafeLockById(id);

        // Assert
        verify(parmSafetyLockMapper).selectByPrimaryKey(id);
        verify(parmSafetyLockMapper).deleteByPrimaryKey(id);
    }

    @Test
    @DisplayName("测试根据ID删除安全开关规则 - 数据不存在异常")
    void testRemoveSafeLockById_DataNotExistsException() {
        // Arrange
        String id = "1";
        
        when(parmSafetyLockMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> safeLockService.removeSafeLockById(id))
                .isInstanceOf(AnyTxnParameterException.class);
        
        verify(parmSafetyLockMapper).selectByPrimaryKey(id);
        verify(parmSafetyLockMapper, never()).deleteByPrimaryKey(id);
    }

    @Test
    @DisplayName("测试根据ID删除安全开关规则 - 删除异常")
    void testRemoveSafeLockById_DeleteException() {
        // Arrange
        String id = "1";
        
        when(parmSafetyLockMapper.selectByPrimaryKey(id)).thenReturn(testParmSafetyLock);
        when(parmSafetyLockMapper.deleteByPrimaryKey(id)).thenThrow(new RuntimeException("删除失败"));

        // Act & Assert
        assertThatThrownBy(() -> safeLockService.removeSafeLockById(id))
                .isInstanceOf(AnyTxnParameterException.class);
        
        verify(parmSafetyLockMapper).selectByPrimaryKey(id);
        verify(parmSafetyLockMapper).deleteByPrimaryKey(id);
    }

    @Test
    @DisplayName("测试根据卡产品编号查询安全开关规则 - 成功场景")
    void testFindSafeLockByCardProNum_Success() {
        // Arrange
        String cardProductCode = "TESTPROD";
        String organizationNumber = "0001";
        List<ParmSafetyLock> parmSafetyLockList = Collections.singletonList(testParmSafetyLock);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmSafetyLock.class), eq(SafeLockDetailDTO.class)))
                    .thenReturn(testSafeLockDetailDTO);
            
            when(parmSafetyLockMapper.selectByCardProductCode(cardProductCode, organizationNumber))
                    .thenReturn(parmSafetyLockList);

            // Act
            SafeLockDTO result = safeLockService.findSafeLockByCardProNum(cardProductCode, organizationNumber);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCardProductCode()).isEqualTo(cardProductCode);
            assertThat(result.getOrganizationNumber()).isEqualTo(organizationNumber);
            assertThat(result.getSfeLockDetailDtos()).hasSize(1);
            
            verify(parmSafetyLockMapper).selectByCardProductCode(cardProductCode, organizationNumber);
        }
    }

    @Test
    @DisplayName("测试根据卡产品编号查询安全开关规则 - 查询异常")
    void testFindSafeLockByCardProNum_QueryException() {
        // Arrange
        String cardProductCode = "TESTPROD";
        String organizationNumber = "0001";
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            when(parmSafetyLockMapper.selectByCardProductCode(cardProductCode, organizationNumber))
                    .thenThrow(new RuntimeException("查询异常"));

            // Act & Assert
            assertThatThrownBy(() -> safeLockService.findSafeLockByCardProNum(cardProductCode, organizationNumber))
                    .isInstanceOf(AnyTxnParameterException.class);
            
            verify(parmSafetyLockMapper).selectByCardProductCode(cardProductCode, organizationNumber);
        }
    }

    @Test
    @DisplayName("测试根据ID查询安全开关规则 - 成功场景")
    void testFindSafeLockById_Success() {
        // Arrange
        String id = "1";
        List<ParmSafetyLock> parmSafetyLockList = Collections.singletonList(testParmSafetyLock);
        
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmSafetyLock.class), eq(SafeLockDTO.class)))
                    .thenReturn(testSafeLockDTO);
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(SafeLockDetailDTO.class)))
                    .thenReturn(Collections.singletonList(testSafeLockDetailDTO));
            
            when(parmSafetyLockMapper.selectByPrimaryKey(id)).thenReturn(testParmSafetyLock);
            when(parmSafetyLockMapper.selectByCardProductCode(testParmSafetyLock.getCardProductCode(), testParmSafetyLock.getOrganizationNumber()))
                    .thenReturn(parmSafetyLockList);

            // Act
            SafeLockDTO result = safeLockService.findSafeLockById(id);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(id);
            assertThat(result.getSfeLockDetailDtos()).hasSize(1);
            
            verify(parmSafetyLockMapper).selectByPrimaryKey(id);
            verify(parmSafetyLockMapper).selectByCardProductCode(testParmSafetyLock.getCardProductCode(), testParmSafetyLock.getOrganizationNumber());
        }
    }

    @Test
    @DisplayName("测试根据ID查询安全开关规则 - 查询异常")
    void testFindSafeLockById_QueryException() {
        // Arrange
        String id = "1";
        
        when(parmSafetyLockMapper.selectByPrimaryKey(id)).thenThrow(new RuntimeException("查询异常"));

        // Act & Assert
        assertThatThrownBy(() -> safeLockService.findSafeLockById(id))
                .isInstanceOf(AnyTxnParameterException.class);
        
        verify(parmSafetyLockMapper).selectByPrimaryKey(id);
    }
}
