﻿package com.anytech.anytxn.parameter.authorization.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.authorization.service.CreditTransactionOverpayLimitServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CreditTransactionOverpayLimitDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.CreditTransactionOverpayLimitMapper;
import com.anytech.anytxn.parameter.authorization.mapper.CreditTransactionOverpayLimitSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.CreditTransactionOverpayLimit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CreditTransactionOverpayLimitServiceImpl 单元测试类
 * 
 * 测试贷方交易类型关联溢缴款额度类型参数服务的核心业务逻辑
 * 
 * <AUTHOR> Generator
 * @date 2025-06-25
 */
@ExtendWith(MockitoExtension.class)
class CreditTransactionOverpayLimitServiceTest {

    @Mock
    private CreditTransactionOverpayLimitMapper creditTransactionOverpayLimitMapper;

    @Mock
    private CreditTransactionOverpayLimitSelfMapper overpayLimitSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CreditTransactionOverpayLimitServiceImpl creditTransactionOverpayLimitService;

    private String testId;
    private String testOrganizationNumber;
    private String testTableId;
    private String testCreditTransactionTypeCode;
    private String testCurrency;
    private CreditTransactionOverpayLimitDTO testCreditTransactionOverpayLimitDTO;
    private CreditTransactionOverpayLimit testCreditTransactionOverpayLimit;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testId = "123456789";
        testOrganizationNumber = "001";
        testTableId = "TBL001";
        testCreditTransactionTypeCode = "CREDIT_TYPE_001";
        testCurrency = "CNY";
        
        // 创建测试的CreditTransactionOverpayLimitDTO对象
        testCreditTransactionOverpayLimitDTO = new CreditTransactionOverpayLimitDTO();
        testCreditTransactionOverpayLimitDTO.setId(testId);
        testCreditTransactionOverpayLimitDTO.setOrganizationNumber(testOrganizationNumber);
        testCreditTransactionOverpayLimitDTO.setTableId(testTableId);
        testCreditTransactionOverpayLimitDTO.setDescription("测试贷方交易溢缴款参数");
        testCreditTransactionOverpayLimitDTO.setCreditTransactionTypeCode(testCreditTransactionTypeCode);
        testCreditTransactionOverpayLimitDTO.setLimitCtrlUnitCode("UNIT001");
        testCreditTransactionOverpayLimitDTO.setStatus("1");
        testCreditTransactionOverpayLimitDTO.setCurrency(testCurrency);
        testCreditTransactionOverpayLimitDTO.setCreateTime(LocalDateTime.now());
        testCreditTransactionOverpayLimitDTO.setUpdateTime(LocalDateTime.now());
        testCreditTransactionOverpayLimitDTO.setUpdateBy("testUser");
        testCreditTransactionOverpayLimitDTO.setVersionNumber(1L);

        // 创建测试的CreditTransactionOverpayLimit对象
        testCreditTransactionOverpayLimit = new CreditTransactionOverpayLimit();
        testCreditTransactionOverpayLimit.setId(testId);
        testCreditTransactionOverpayLimit.setOrganizationNumber(testOrganizationNumber);
        testCreditTransactionOverpayLimit.setTableId(testTableId);
        testCreditTransactionOverpayLimit.setDescription("测试贷方交易溢缴款参数");
        testCreditTransactionOverpayLimit.setCreditTransactionTypeCode(testCreditTransactionTypeCode);
        testCreditTransactionOverpayLimit.setLimitCtrlUnitCode("UNIT001");
        testCreditTransactionOverpayLimit.setStatus("1");
        testCreditTransactionOverpayLimit.setCurrency(testCurrency);
        testCreditTransactionOverpayLimit.setCreateTime(LocalDateTime.now());
        testCreditTransactionOverpayLimit.setUpdateTime(LocalDateTime.now());
        testCreditTransactionOverpayLimit.setUpdateBy("testUser");
        testCreditTransactionOverpayLimit.setVersionNumber(1L);
    }

    /**
     * 测试 add - 成功添加
     */
    @Test
    void testAdd_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {

            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(testOrganizationNumber)).thenReturn(testOrganizationNumber);
            beanMappingMock.when(() -> BeanMapping.copy(any(CreditTransactionOverpayLimitDTO.class), eq(CreditTransactionOverpayLimit.class)))
                    .thenReturn(testCreditTransactionOverpayLimit);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(creditTransactionOverpayLimitMapper.selectByTableIdAndOrg(testTableId, testOrganizationNumber))
                    .thenReturn(null);
            when(overpayLimitSelfMapper.selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency))
                    .thenReturn(null);
            when(numberIdGenerator.generateId("tenant001")).thenReturn(123456789L);

            // Act
            ParameterCompare result = creditTransactionOverpayLimitService.add(testCreditTransactionOverpayLimitDTO);

            // Assert
            assertNotNull(result);
            verify(creditTransactionOverpayLimitMapper).selectByTableIdAndOrg(testTableId, testOrganizationNumber);
            verify(overpayLimitSelfMapper).selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency);
            verify(numberIdGenerator).generateId("tenant001");
            orgUtilsMock.verify(() -> OrgNumberUtils.getOrg(testOrganizationNumber));
            beanMappingMock.verify(() -> BeanMapping.copy(any(CreditTransactionOverpayLimitDTO.class), eq(CreditTransactionOverpayLimit.class)));
            tenantUtilsMock.verify(TenantUtils::getTenantId);
        }
    }

    /**
     * 测试 add - 参数为空
     */
    @Test
    void testAdd_EmptyDTO() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.add(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add - TableId为空
     */
    @Test
    void testAdd_EmptyTableId() {
        // Arrange
        testCreditTransactionOverpayLimitDTO.setTableId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.add(testCreditTransactionOverpayLimitDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add - 币种为空
     */
    @Test
    void testAdd_EmptyCurrency() {
        // Arrange
        testCreditTransactionOverpayLimitDTO.setCurrency(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.add(testCreditTransactionOverpayLimitDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add - 贷方交易类型为空
     */
    @Test
    void testAdd_EmptyCreditTransactionTypeCode() {
        // Arrange
        testCreditTransactionOverpayLimitDTO.setCreditTransactionTypeCode(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.add(testCreditTransactionOverpayLimitDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 add - 记录已存在（根据TableId和机构号）
     */
    @Test
    void testAdd_RecordExistsByTableId() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(testOrganizationNumber)).thenReturn(testOrganizationNumber);
            when(creditTransactionOverpayLimitMapper.selectByTableIdAndOrg(testTableId, testOrganizationNumber))
                    .thenReturn(testCreditTransactionOverpayLimit);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                    creditTransactionOverpayLimitService.add(testCreditTransactionOverpayLimitDTO));

            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode(), exception.getErrCode());
            verify(creditTransactionOverpayLimitMapper).selectByTableIdAndOrg(testTableId, testOrganizationNumber);
            orgUtilsMock.verify(() -> OrgNumberUtils.getOrg(testOrganizationNumber));
        }
    }

    /**
     * 测试 add - 记录已存在（根据机构号+交易类型+币种）
     */
    @Test
    void testAdd_RecordExistsByOrgTransTypeCurrency() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(testOrganizationNumber)).thenReturn(testOrganizationNumber);
            when(creditTransactionOverpayLimitMapper.selectByTableIdAndOrg(testTableId, testOrganizationNumber))
                    .thenReturn(null);
            when(overpayLimitSelfMapper.selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency))
                    .thenReturn(testCreditTransactionOverpayLimit);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                    creditTransactionOverpayLimitService.add(testCreditTransactionOverpayLimitDTO));

            assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT.getCode(), exception.getErrCode());
            verify(creditTransactionOverpayLimitMapper).selectByTableIdAndOrg(testTableId, testOrganizationNumber);
            verify(overpayLimitSelfMapper).selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency);
            orgUtilsMock.verify(() -> OrgNumberUtils.getOrg(testOrganizationNumber));
        }
    }

    /**
     * 测试 modify - 成功修改
     */
    @Test
    void testModify_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CreditTransactionOverpayLimitDTO.class), eq(CreditTransactionOverpayLimit.class)))
                    .thenReturn(testCreditTransactionOverpayLimit);
            when(creditTransactionOverpayLimitMapper.selectByPrimaryKey(testId)).thenReturn(testCreditTransactionOverpayLimit);

            // Act
            ParameterCompare result = creditTransactionOverpayLimitService.modify(testCreditTransactionOverpayLimitDTO);

            // Assert
            assertNotNull(result);
            verify(creditTransactionOverpayLimitMapper).selectByPrimaryKey(testId);
            beanMappingMock.verify(() -> BeanMapping.copy(any(CreditTransactionOverpayLimitDTO.class), eq(CreditTransactionOverpayLimit.class)));
        }
    }

    /**
     * 测试 modify - 参数为空
     */
    @Test
    void testModify_EmptyDTO() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.modify(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 modify - 记录不存在
     */
    @Test
    void testModify_RecordNotFound() {
        // Arrange
        when(creditTransactionOverpayLimitMapper.selectByPrimaryKey(testId)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.modify(testCreditTransactionOverpayLimitDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
        verify(creditTransactionOverpayLimitMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试 remove - 成功删除
     */
    @Test
    void testRemove_Success() {
        // Arrange
        when(creditTransactionOverpayLimitMapper.selectByPrimaryKey(testId)).thenReturn(testCreditTransactionOverpayLimit);

        // Act
        ParameterCompare result = creditTransactionOverpayLimitService.remove(testId);

        // Assert
        assertNotNull(result);
        verify(creditTransactionOverpayLimitMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试 remove - ID为空
     */
    @Test
    void testRemove_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.remove(""));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 remove - 记录不存在
     */
    @Test
    void testRemove_RecordNotFound() {
        // Arrange
        when(creditTransactionOverpayLimitMapper.selectByPrimaryKey(testId)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.remove(testId));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
        verify(creditTransactionOverpayLimitMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试 findById - 成功查询
     */
    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CreditTransactionOverpayLimit.class), eq(CreditTransactionOverpayLimitDTO.class)))
                    .thenReturn(testCreditTransactionOverpayLimitDTO);
            when(creditTransactionOverpayLimitMapper.selectByPrimaryKey(testId)).thenReturn(testCreditTransactionOverpayLimit);

            // Act
            CreditTransactionOverpayLimitDTO result = creditTransactionOverpayLimitService.findById(testId);

            // Assert
            assertNotNull(result);
            assertEquals(testId, result.getId());
            verify(creditTransactionOverpayLimitMapper).selectByPrimaryKey(testId);
            beanMappingMock.verify(() -> BeanMapping.copy(any(CreditTransactionOverpayLimit.class), eq(CreditTransactionOverpayLimitDTO.class)));
        }
    }

    /**
     * 测试 findById - ID为空
     */
    @Test
    void testFindById_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.findById(""));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findPage - 成功分页查询
     */
    @Test
    void testFindPage_Success() {
        // Arrange
        List<CreditTransactionOverpayLimit> mockList = Arrays.asList(testCreditTransactionOverpayLimit);
        List<CreditTransactionOverpayLimitDTO> mockDTOList = Arrays.asList(testCreditTransactionOverpayLimitDTO);

        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(testOrganizationNumber)).thenReturn(testOrganizationNumber);
            beanMappingMock.when(() -> BeanMapping.copyList(mockList, CreditTransactionOverpayLimitDTO.class))
                    .thenReturn(mockDTOList);
            when(creditTransactionOverpayLimitMapper.selectByConditionAndPage(testTableId, "测试描述", testOrganizationNumber))
                    .thenReturn(mockList);

            // Act
            PageResultDTO<CreditTransactionOverpayLimitDTO> result = creditTransactionOverpayLimitService.findPage(1, 10, testOrganizationNumber, testTableId, "测试描述");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            verify(creditTransactionOverpayLimitMapper).selectByConditionAndPage(testTableId, "测试描述", testOrganizationNumber);
            orgUtilsMock.verify(() -> OrgNumberUtils.getOrg(testOrganizationNumber));
            beanMappingMock.verify(() -> BeanMapping.copyList(mockList, CreditTransactionOverpayLimitDTO.class));
        }
    }

    /**
     * 测试 findByOrgNumAndTransTypeCodeAndCurr - 成功查询
     */
    @Test
    void testFindByOrgNumAndTransTypeCodeAndCurr_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(CreditTransactionOverpayLimit.class), eq(CreditTransactionOverpayLimitDTO.class)))
                    .thenReturn(testCreditTransactionOverpayLimitDTO);
            when(overpayLimitSelfMapper.selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency))
                    .thenReturn(testCreditTransactionOverpayLimit);

            // Act
            CreditTransactionOverpayLimitDTO result = creditTransactionOverpayLimitService.findByOrgNumAndTransTypeCodeAndCurr(
                    testOrganizationNumber, testCreditTransactionTypeCode, testCurrency);

            // Assert
            assertNotNull(result);
            assertEquals(testId, result.getId());
            verify(overpayLimitSelfMapper).selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency);
            beanMappingMock.verify(() -> BeanMapping.copy(any(CreditTransactionOverpayLimit.class), eq(CreditTransactionOverpayLimitDTO.class)));
        }
    }

    /**
     * 测试 findByOrgNumAndTransTypeCodeAndCurr - 机构号为空
     */
    @Test
    void testFindByOrgNumAndTransTypeCodeAndCurr_EmptyOrgNum() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.findByOrgNumAndTransTypeCodeAndCurr("", testCreditTransactionTypeCode, testCurrency));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByOrgNumAndTransTypeCodeAndCurr - 交易类型为空
     */
    @Test
    void testFindByOrgNumAndTransTypeCodeAndCurr_EmptyTransTypeCode() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.findByOrgNumAndTransTypeCodeAndCurr(testOrganizationNumber, "", testCurrency));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByOrgNumAndTransTypeCodeAndCurr - 币种为空
     */
    @Test
    void testFindByOrgNumAndTransTypeCodeAndCurr_EmptyCurrency() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.findByOrgNumAndTransTypeCodeAndCurr(testOrganizationNumber, testCreditTransactionTypeCode, ""));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试 findByOrgNumAndTransTypeCodeAndCurr - 记录不存在
     */
    @Test
    void testFindByOrgNumAndTransTypeCodeAndCurr_RecordNotFound() {
        // Arrange
        when(overpayLimitSelfMapper.selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                creditTransactionOverpayLimitService.findByOrgNumAndTransTypeCodeAndCurr(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
        verify(overpayLimitSelfMapper).selectByOrgNumAndTransTypeCodeAndCurrency(testOrganizationNumber, testCreditTransactionTypeCode, testCurrency);
    }
} 
